from flask import Blueprint, session, request, jsonify, send_file, render_template, redirect
from app.config import get_db_connection, Config
from werkzeug.security import generate_password_hash
from app.services.audit_log import AuditLogService
import pandas as pd
import tempfile
import os
from datetime import datetime, timedelta
import pymysql
import io
import time
import re
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
from io import BytesIO
from app.services.sample_notification_service import SampleNotificationService
# from sqlalchemy import func

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/get_account_bindings', methods=['GET'])
def get_account_bindings():
    """
    获取账号绑定列表
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        search: 搜索关键词
    返回:
        绑定列表、总数
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        search = request.args.get('search', '').strip()

        # 防止参数错误
        if page < 1:
            page = 1
        if limit < 1 or limit > 100:
            limit = 10

        offset = (page - 1) * limit

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 构建查询条件
        where_conditions = ["ab.status IN (0, 1)"]
        params = []

        if search:
            where_conditions.append("""
                (du.username LIKE %s OR du.name LIKE %s OR dc.name LIKE %s OR
                 pu.username LIKE %s OR pu.name LIKE %s OR pc.name LIKE %s)
            """)
            search_param = f'%{search}%'
            params.extend([search_param] * 6)

        where_clause = " AND ".join(where_conditions)

        # 查询绑定列表
        query = f"""
        SELECT
            ab.id,
            ab.dealer_user_id,
            ab.publisher_user_id,
            ab.status,
            ab.created_at,
            ab.updated_at,
            du.username as dealer_username,
            du.name as dealer_name,
            dc.name as dealer_company_name,
            pu.username as publisher_username,
            pu.name as publisher_name,
            pc.name as publisher_company_name,
            cu.username as creator_username
        FROM account_bindings ab
        LEFT JOIN users du ON ab.dealer_user_id = du.user_id
        LEFT JOIN users pu ON ab.publisher_user_id = pu.user_id
        LEFT JOIN dealer_companies dc ON du.dealer_company_id = dc.id
        LEFT JOIN publisher_companies pc ON pu.publisher_company_id = pc.id
        LEFT JOIN users cu ON ab.created_by = cu.user_id
        WHERE {where_clause}
        ORDER BY ab.created_at DESC
        LIMIT %s OFFSET %s
        """

        cursor.execute(query, params + [limit, offset])
        bindings = cursor.fetchall()

        # 查询总数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM account_bindings ab
        LEFT JOIN users du ON ab.dealer_user_id = du.user_id
        LEFT JOIN users pu ON ab.publisher_user_id = pu.user_id
        LEFT JOIN dealer_companies dc ON du.dealer_company_id = dc.id
        LEFT JOIN publisher_companies pc ON pu.publisher_company_id = pc.id
        WHERE {where_clause}
        """

        cursor.execute(count_query, params)
        total_result = cursor.fetchone()
        total = total_result['total'] if total_result else 0

        cursor.close()
        connection.close()

        # 格式化返回数据
        for binding in bindings:
            if binding['created_at']:
                binding['created_at'] = binding['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if binding['updated_at']:
                binding['updated_at'] = binding['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "bindings": bindings,
                "total": total,
                "page": page,
                "limit": limit,
                "total_pages": (total + limit - 1) // limit
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取绑定列表失败: {str(e)}"})

@admin_bp.route('/get_users_for_binding', methods=['GET'])
def get_users_for_binding():
    """
    获取可用于绑定的用户列表
    请求参数:
        role: 用户角色 (dealer/publisher)
    返回:
        用户列表
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        role = request.args.get('role', '')
        if role not in ['dealer', 'publisher']:
            return jsonify({"code": 1, "message": "角色参数无效"})

        # 建立数据库连接
        connection = None
        cursor = None

        try:
            connection = get_db_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            if role == 'dealer':
                # 获取未绑定的经销商用户（排除已经作为经销商或供应商绑定的用户）
                query = """
                SELECT u.user_id, u.username, u.name, dc.name as company_name
                FROM users u
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                WHERE u.role = 'dealer'
                AND u.user_id NOT IN (
                    SELECT dealer_user_id FROM account_bindings WHERE status = 1
                    UNION
                    SELECT publisher_user_id FROM account_bindings WHERE status = 1
                )
                ORDER BY u.username
                """
            else:
                # 获取未绑定的供应商用户（排除已经作为经销商或供应商绑定的用户）
                query = """
                SELECT u.user_id, u.username, u.name, pc.name as company_name
                FROM users u
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                WHERE u.role = 'publisher'
                AND u.user_id NOT IN (
                    SELECT dealer_user_id FROM account_bindings WHERE status = 1
                    UNION
                    SELECT publisher_user_id FROM account_bindings WHERE status = 1
                )
                ORDER BY u.username
                """

            cursor.execute(query)
            users = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": users
            })

        except Exception as db_error:
            print(f"数据库操作错误 (role={role}): {str(db_error)}")
            return jsonify({"code": 1, "message": f"数据库查询失败: {str(db_error)}"})

        finally:
            # 确保连接被正确关闭
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    except Exception as e:
        print(f"获取用户列表失败 (role={role}): {str(e)}")
        return jsonify({"code": 1, "message": f"获取用户列表失败: {str(e)}"})

@admin_bp.route('/get_companies_for_binding', methods=['GET'])
def get_companies_for_binding():
    """
    获取可用于绑定的单位列表
    请求参数:
        type: 单位类型 (dealer/publisher)
    返回:
        单位列表
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        company_type = request.args.get('type', '')
        if company_type not in ['dealer', 'publisher']:
            return jsonify({"code": 1, "message": "单位类型参数无效"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        if company_type == 'dealer':
            query = "SELECT id, name FROM dealer_companies ORDER BY name"
        else:
            query = "SELECT id, name FROM publisher_companies ORDER BY name"

        cursor.execute(query)
        companies = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": companies
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取单位列表失败: {str(e)}"})

@admin_bp.route('/create_account_binding', methods=['POST'])
def create_account_binding():
    """
    创建账号绑定
    请求数据:
        dealer_type: 经销商类型 (existing/new)
        publisher_type: 供应商类型 (existing/new)
        dealer_user_id: 经销商用户ID (existing时必填)
        publisher_user_id: 供应商用户ID (existing时必填)
        dealer_username: 经销商用户名 (new时必填)
        dealer_password: 经销商密码 (new时必填)
        dealer_name: 经销商姓名
        dealer_phone: 经销商手机号
        dealer_company_id: 经销商单位ID
        publisher_username: 供应商用户名 (new时必填)
        publisher_password: 供应商密码 (new时必填)
        publisher_name: 供应商姓名
        publisher_phone: 供应商手机号
        publisher_company_id: 供应商单位ID
        status: 绑定状态
    返回:
        创建结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        dealer_type = data.get('dealer_type', 'existing')
        publisher_type = data.get('publisher_type', 'existing')
        status = int(data.get('status', 1))
        current_user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        try:
            # 开始事务
            connection.begin()

            # 处理经销商用户
            if dealer_type == 'existing':
                dealer_user_id = data.get('dealer_user_id')
                if not dealer_user_id:
                    return jsonify({"code": 1, "message": "请选择经销商用户"})
            else:
                # 如果供应商是现有用户，获取其ID用于单位自动创建
                other_user_id = data.get('publisher_user_id') if publisher_type == 'existing' else None
                # 创建新的经销商用户
                dealer_user_id, error_msg = create_new_user(cursor, data, 'dealer', other_user_id)
                if not dealer_user_id:
                    connection.rollback()
                    return jsonify({"code": 1, "message": f"创建经销商用户失败: {error_msg}"})

            # 处理供应商用户
            if publisher_type == 'existing':
                publisher_user_id = data.get('publisher_user_id')
                if not publisher_user_id:
                    return jsonify({"code": 1, "message": "请选择供应商用户"})
            else:
                # 如果经销商是现有用户，获取其ID用于单位自动创建
                other_user_id = dealer_user_id if dealer_type == 'existing' else None
                # 创建新的供应商用户
                publisher_user_id, error_msg = create_new_user(cursor, data, 'publisher', other_user_id)
                if not publisher_user_id:
                    connection.rollback()
                    print(f"创建供应商用户失败，数据: {data}")
                    return jsonify({"code": 1, "message": f"创建供应商用户失败: {error_msg}"})

            # 检查用户是否已经被绑定（一个用户只能有一个绑定关系）
            dealer_check_query = """
            SELECT id FROM account_bindings
            WHERE (dealer_user_id = %s OR publisher_user_id = %s) AND status = 1
            """
            cursor.execute(dealer_check_query, (dealer_user_id, dealer_user_id))
            dealer_existing = cursor.fetchone()

            if dealer_existing:
                connection.rollback()
                return jsonify({"code": 1, "message": "该经销商用户已经被绑定，一个用户只能有一个绑定关系"})

            publisher_check_query = """
            SELECT id FROM account_bindings
            WHERE (dealer_user_id = %s OR publisher_user_id = %s) AND status = 1
            """
            cursor.execute(publisher_check_query, (publisher_user_id, publisher_user_id))
            publisher_existing = cursor.fetchone()

            if publisher_existing:
                connection.rollback()
                return jsonify({"code": 1, "message": "该供应商用户已经被绑定，一个用户只能有一个绑定关系"})

            # 检查具体的绑定关系是否已存在
            specific_check_query = """
            SELECT id FROM account_bindings
            WHERE dealer_user_id = %s AND publisher_user_id = %s
            """
            cursor.execute(specific_check_query, (dealer_user_id, publisher_user_id))
            specific_existing = cursor.fetchone()

            if specific_existing:
                connection.rollback()
                return jsonify({"code": 1, "message": "该绑定关系已存在"})

            # 创建绑定关系
            insert_query = """
            INSERT INTO account_bindings (dealer_user_id, publisher_user_id, status, created_by)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(insert_query, (dealer_user_id, publisher_user_id, status, current_user_id))

            # 提交事务
            connection.commit()

            cursor.close()
            connection.close()

            return jsonify({
                "code": 0,
                "message": "账号绑定创建成功"
            })

        except Exception as e:
            connection.rollback()
            raise e

    except Exception as e:
        return jsonify({"code": 1, "message": f"创建绑定失败: {str(e)}"})

def create_new_user(cursor, data, role, other_role_user_id=None):
    """
    创建新用户的辅助函数
    返回: (user_id, error_message) 或 (None, error_message)
    """
    try:
        if role == 'dealer':
            username = data.get('dealer_username', '').strip()
            password = data.get('dealer_password', '').strip()
            name = data.get('dealer_name', '').strip()
            phone = data.get('dealer_phone', '').strip()
        else:  # publisher
            username = data.get('publisher_username', '').strip()
            password = data.get('publisher_password', '').strip()
            name = data.get('publisher_name', '').strip()
            phone = data.get('publisher_phone', '').strip()

        # 验证必填字段
        if not username or not password:
            error_msg = f"必填字段缺失: username={username}, password={'***' if password else 'None'}"
            print(error_msg)
            return None, error_msg

        # 处理空字符串字段，避免唯一约束冲突
        phone = phone if phone else None
        name = name if name else None

        # 检查用户名是否已存在
        check_query = "SELECT user_id FROM users WHERE username = %s"
        cursor.execute(check_query, (username,))
        existing_user = cursor.fetchone()
        if existing_user:
            error_msg = f"用户名已存在: {username}"
            print(error_msg)
            return None, error_msg

        # 检查手机号是否已存在（如果提供了手机号）
        if phone:
            check_query = "SELECT user_id FROM users WHERE phone_number = %s"
            cursor.execute(check_query, (phone,))
            existing_phone = cursor.fetchone()
            if existing_phone:
                error_msg = f"手机号已注册: {phone}"
                print(error_msg)
                return None, error_msg

        # 生成密码哈希
        password_hash = generate_password_hash(password)

        # 获取对方用户的单位信息，用于自动创建单位
        company_id = None
        if other_role_user_id:
            company_id = get_or_create_company_for_user(cursor, other_role_user_id, role)

        # 插入用户记录
        print(f"准备插入用户: username={username}, name={name}, phone={phone}, role={role}, company_id={company_id}")

        if role == 'dealer':
            insert_query = """
            INSERT INTO users (username, password, name, phone_number, role, dealer_company_id)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (username, password_hash, name, phone, role, company_id))
        else:
            insert_query = """
            INSERT INTO users (username, password, name, phone_number, role, publisher_company_id)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (username, password_hash, name, phone, role, company_id))

        # 获取新创建的用户ID
        user_id = cursor.lastrowid

        # 如果是出版社用户，创建默认目录
        if role == 'publisher':
            try:
                cursor.execute("""
                    INSERT INTO directories (name, parent_id, publisher_id)
                    VALUES (%s, %s, %s)
                """, ('默认目录', None, user_id))
                print(f"为出版社用户 {user_id} 创建默认目录成功 (create_new_user)")
            except Exception as e:
                print(f"为出版社用户 {user_id} 创建默认目录失败 (create_new_user): {str(e)}")
                # 目录创建失败不影响用户创建成功

        return user_id, None

    except Exception as e:
        error_msg = f"创建用户失败 (role={role}): {str(e)}"
        print(error_msg)
        print(f"用户数据: username={username}, name={name}, phone={phone}")
        return None, error_msg

def get_or_create_company_for_user(cursor, user_id, target_role):
    """
    根据用户ID获取或创建对应角色的单位
    """
    try:
        # 获取用户信息
        user_query = """
        SELECT u.role, u.dealer_company_id, u.publisher_company_id,
               dc.name as dealer_company_name, pc.name as publisher_company_name
        FROM users u
        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
        WHERE u.user_id = %s
        """
        cursor.execute(user_query, (user_id,))
        user_info = cursor.fetchone()

        if not user_info:
            return None

        # 获取源单位名称
        source_company_name = None
        if user_info['role'] == 'dealer' and user_info['dealer_company_name']:
            source_company_name = user_info['dealer_company_name']
        elif user_info['role'] == 'publisher' and user_info['publisher_company_name']:
            source_company_name = user_info['publisher_company_name']

        if not source_company_name:
            return None

        # 在目标角色的单位表中查找或创建单位
        if target_role == 'dealer':
            # 检查经销商单位表中是否存在
            check_query = "SELECT id FROM dealer_companies WHERE name = %s"
            cursor.execute(check_query, (source_company_name,))
            existing_company = cursor.fetchone()

            if existing_company:
                return existing_company['id']
            else:
                # 创建新的经销商单位
                insert_query = "INSERT INTO dealer_companies (name) VALUES (%s)"
                cursor.execute(insert_query, (source_company_name,))
                return cursor.lastrowid
        else:  # publisher
            # 检查供应商单位表中是否存在
            check_query = "SELECT id FROM publisher_companies WHERE name = %s"
            cursor.execute(check_query, (source_company_name,))
            existing_company = cursor.fetchone()

            if existing_company:
                return existing_company['id']
            else:
                # 创建新的供应商单位
                insert_query = "INSERT INTO publisher_companies (name) VALUES (%s)"
                cursor.execute(insert_query, (source_company_name,))
                return cursor.lastrowid

    except Exception as e:
        return None



@admin_bp.route('/update_account_binding', methods=['POST'])
def update_account_binding():
    """
    更新账号绑定
    请求数据:
        binding_id: 绑定ID
        status: 绑定状态
    返回:
        更新结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        binding_id = data.get('binding_id')
        status = int(data.get('status', 1))

        if not binding_id:
            return jsonify({"code": 1, "message": "绑定ID不能为空"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 更新绑定状态
        update_query = """
        UPDATE account_bindings
        SET status = %s, updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """
        cursor.execute(update_query, (status, binding_id))

        if cursor.rowcount == 0:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "绑定记录不存在"})

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "绑定状态更新成功"
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"更新绑定失败: {str(e)}"})

@admin_bp.route('/delete_account_binding', methods=['POST'])
def delete_account_binding():
    """
    删除账号绑定
    请求数据:
        binding_id: 绑定ID
    返回:
        删除结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        binding_id = data.get('binding_id')

        if not binding_id:
            return jsonify({"code": 1, "message": "绑定ID不能为空"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 删除绑定记录
        delete_query = "DELETE FROM account_bindings WHERE id = %s"
        cursor.execute(delete_query, (binding_id,))

        if cursor.rowcount == 0:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "绑定记录不存在"})

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "绑定删除成功"
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"删除绑定失败: {str(e)}"})

@admin_bp.route('/get_exhibitions', methods=['GET'])
def get_exhibitions():
    """
    获取所有书展活动列表
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        tab: 标签筛选(all, draft, published, cancelled, ended)
        search: 搜索关键词
        start_date: 开始日期筛选
        end_date: 结束日期筛选
        school_id: 学校ID筛选
    返回:
        书展列表、状态统计、总数
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        tab = request.args.get('tab', 'all')
        search = request.args.get('search', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        school_id = request.args.get('school_id', '')
        
        # 防止参数错误
        if page < 1:
            page = 1
        if limit < 1 or limit > 50:
            limit = 10
            
        offset = (page - 1) * limit
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建查询条件
        where_clauses = []
        params = []
        
        # 根据标签筛选
        if tab == 'draft':
            where_clauses.append("be.status = 'draft'")
        elif tab == 'published':
            where_clauses.append("be.status = 'published'")
        elif tab == 'cancelled':
            where_clauses.append("be.status = 'cancelled'")
        elif tab == 'ended':
            where_clauses.append("be.status = 'ended'")
        
        # 关键词搜索
        if search:
            where_clauses.append("(be.title LIKE %s OR be.description LIKE %s OR s.name LIKE %s)")
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param])
        
        # 日期筛选
        if start_date:
            where_clauses.append("be.start_time >= %s")
            params.append(start_date)
        if end_date:
            where_clauses.append("be.end_time <= %s")
            params.append(f"{end_date} 23:59:59")
            
        # 学校筛选
        if school_id and school_id != 'all':
            where_clauses.append("be.school_id = %s")
            params.append(school_id)
            
        # 构建WHERE子句
        where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"
        
        # 查询书展列表
        query = f"""
        SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time, 
               be.registration_deadline, be.location, be.status, s.name as school_name,
               ei.name as contact_name, ei.phone as contact_phone, be.created_at,
               be.school_address,
               (SELECT COUNT(*) FROM exhibition_registrations er WHERE er.exhibition_id = be.id AND er.status = 'registered') as registrations_count,
               (SELECT COUNT(*) 
                FROM exhibition_participants ep 
                JOIN exhibition_registrations er ON ep.registration_id = er.id 
                WHERE er.exhibition_id = be.id AND er.status = 'registered') as participants_count
        FROM book_exhibitions be
        JOIN schools s ON be.school_id = s.id
        LEFT JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
        WHERE {where_sql}
        ORDER BY be.created_at DESC
        LIMIT %s, %s
        """
        
        params.append(offset)
        params.append(limit)
        
        cursor.execute(query, params)
        exhibitions = cursor.fetchall()
        
        # 格式化时间
        for exhibition in exhibitions:
            exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M') if exhibition['start_time'] else ''
            exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M') if exhibition['end_time'] else ''
            exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M') if exhibition['registration_deadline'] else ''
            exhibition['created_at'] = exhibition['created_at'].strftime('%Y-%m-%d') if exhibition['created_at'] else ''
        
        # 查询总数
        count_params = params[:-2]  # 去掉 LIMIT 相关参数
        count_query = f"""
        SELECT COUNT(be.id) as total
        FROM book_exhibitions be
        JOIN schools s ON be.school_id = s.id
        LEFT JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
        WHERE {where_sql}
        """
        
        cursor.execute(count_query, count_params)
        total = cursor.fetchone()['total']
        
        # 查询各状态展览数量
        status_counts = {}
        
        # 全部
        cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions")
        status_counts['all'] = cursor.fetchone()['count']
        
        # 草稿
        cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE status = 'draft'")
        status_counts['draft'] = cursor.fetchone()['count']
        
        # 已发布
        cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE status = 'published'")
        status_counts['published'] = cursor.fetchone()['count']
        
        # 已取消
        cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE status = 'cancelled'")
        status_counts['cancelled'] = cursor.fetchone()['count']
        
        # 已结束
        cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE status = 'ended'")
        status_counts['ended'] = cursor.fetchone()['count']
        
        cursor.close()
        connection.close()
        
        result = {
            "exhibitions": exhibitions,
            "status_counts": status_counts,
            "total": total
        }
        
        return jsonify({"code": 0, "message": "获取成功", "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展列表失败: {str(e)}"})

@admin_bp.route('/get_exhibition_detail', methods=['GET'])
def get_exhibition_detail():
    """
    获取书展详情
    请求参数:
        id: 书展ID
    返回:
        书展详细信息
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        exhibition_id = request.args.get('id')
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询书展详情
        query = """
        SELECT be.*, s.name as school_name, s.id as school_id, 
               u.username as initiator_username, u.name as initiator_realname
        FROM book_exhibitions be
        JOIN schools s ON be.school_id = s.id
        JOIN users u ON be.initiator_id = u.user_id
        WHERE be.id = %s
        """
        cursor.execute(query, (exhibition_id,))
        exhibition = cursor.fetchone()
        
        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在"})
        
        # 格式化时间
        exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M') if exhibition['start_time'] else ''
        exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M') if exhibition['end_time'] else ''
        exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M') if exhibition['registration_deadline'] else ''
        exhibition['created_at'] = exhibition['created_at'].strftime('%Y-%m-%d %H:%M:%S') if exhibition['created_at'] else ''
        exhibition['updated_at'] = exhibition['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if exhibition['updated_at'] else ''
        
        # 查询发起人信息
        query = """
        SELECT name, phone, department, position, email
        FROM exhibition_initiators
        WHERE exhibition_id = %s
        """
        cursor.execute(query, (exhibition_id,))
        initiator = cursor.fetchone()
        
        if initiator:
            exhibition['initiator'] = initiator
        else:
            exhibition['initiator'] = {
                'name': '未知',
                'phone': '未知'
            }
            
        # 查询报名情况
        query = """
        SELECT er.id, er.publisher_id, er.status, er.created_at, er.updated_at,
               u.username, u.name as publisher_name, u.phone_number,
               CASE 
                   WHEN u.role = 'publisher' THEN pc.name
                   WHEN u.role = 'dealer' THEN dc.name
                   ELSE '未知'
               END as company_name
        FROM exhibition_registrations er
        JOIN users u ON er.publisher_id = u.user_id
        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
        WHERE er.exhibition_id = %s
        ORDER BY er.status ASC, er.created_at DESC
        """
        cursor.execute(query, (exhibition_id,))
        registrations = cursor.fetchall()
        
        for reg in registrations:
            reg['created_at'] = reg['created_at'].strftime('%Y-%m-%d %H:%M:%S') if reg['created_at'] else ''
            reg['updated_at'] = reg['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if reg['updated_at'] else ''
            
            # 查询参展人员
            query = """
            SELECT id, name, phone, role, is_contact
            FROM exhibition_participants
            WHERE registration_id = %s
            ORDER BY is_contact DESC, id ASC
            """
            cursor.execute(query, (reg['id'],))
            participants = cursor.fetchall()
            reg['participants'] = participants
            
            # 设置联系人信息
            contact_participant = None
            for participant in participants:
                if participant['is_contact']:
                    contact_participant = participant
                    break
            
            if contact_participant:
                reg['contact_name'] = contact_participant['name']
                reg['contact_phone'] = contact_participant['phone']
            else:
                reg['contact_name'] = reg['publisher_name']
                reg['contact_phone'] = reg['phone_number']
        
        exhibition['registrations'] = registrations
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": exhibition})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展详情失败: {str(e)}"})

@admin_bp.route('/change_exhibition_status', methods=['POST'])
def change_exhibition_status():
    """
    修改书展状态
    请求数据:
        id: 书展ID
        status: 目标状态 (draft, published, cancelled, ended)
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        exhibition_id = data.get('id')
        new_status = data.get('status')
        
        if not exhibition_id or not new_status:
            return jsonify({"code": 1, "message": "参数错误"})
        
        if new_status not in ['draft', 'published', 'cancelled', 'ended']:
            return jsonify({"code": 1, "message": "状态值无效"})
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 先检查书展是否存在
        cursor.execute("SELECT id, status FROM book_exhibitions WHERE id = %s", (exhibition_id,))
        exhibition = cursor.fetchone()
        
        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在"})
        
        # 修改状态
        cursor.execute(
            "UPDATE book_exhibitions SET status = %s, updated_at = NOW() WHERE id = %s",
            (new_status, exhibition_id)
        )
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": f"书展状态已更新为{new_status}"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"修改书展状态失败: {str(e)}"})

@admin_bp.route('/delete_exhibition', methods=['POST'])
def delete_exhibition():
    """
    删除书展活动
    请求数据:
        id: 书展ID
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        exhibition_id = data.get('id')
        
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查书展是否存在
        cursor.execute("SELECT id FROM book_exhibitions WHERE id = %s", (exhibition_id,))
        if not cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在"})
        
        # 先删除相关联的数据(因为有外键约束)
        # 删除参展人员 - 先获取所有报名ID
        cursor.execute("SELECT id FROM exhibition_registrations WHERE exhibition_id = %s", (exhibition_id,))
        registrations = cursor.fetchall()
        
        for reg in registrations:
            cursor.execute("DELETE FROM exhibition_participants WHERE registration_id = %s", (reg['id'],))
        
        # 删除报名信息
        cursor.execute("DELETE FROM exhibition_registrations WHERE exhibition_id = %s", (exhibition_id,))
        
        # 删除发起人信息
        cursor.execute("DELETE FROM exhibition_initiators WHERE exhibition_id = %s", (exhibition_id,))
        
        # 最后删除书展本身
        cursor.execute("DELETE FROM book_exhibitions WHERE id = %s", (exhibition_id,))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "书展已成功删除"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除书展失败: {str(e)}"})

@admin_bp.route('/get_exhibition_participants', methods=['GET'])
def get_exhibition_participants():
    """
    获取书展参展人员列表
    请求参数:
        exhibition_id: 书展ID
    返回:
        参展机构和人员列表
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        exhibition_id = request.args.get('exhibition_id')
        # print(f"获取参展人员: exhibition_id={exhibition_id}")
        
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 首先检查展览是否存在
        cursor.execute("SELECT id, title FROM book_exhibitions WHERE id = %s", (exhibition_id,))
        exhibition = cursor.fetchone()
        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "展览不存在"})
        
        # print(f"展览ID {exhibition_id}: {exhibition['title']}")
        
        # 获取所有报名记录，包括已取消的
        registrations_query = """
        SELECT er.id as registration_id, 
               er.publisher_id, 
               u.username, 
               u.name as publisher_name,
               u.role,
               CASE 
                   WHEN u.role = 'publisher' THEN pc.name
                   WHEN u.role = 'dealer' THEN dc.name
                   ELSE '未知'
               END as company_name,
               er.created_at as registration_time,
               er.status as registration_status
        FROM exhibition_registrations er
        JOIN users u ON er.publisher_id = u.user_id
        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
        WHERE er.exhibition_id = %s
        ORDER BY er.status ASC, er.created_at DESC
        """
        cursor.execute(registrations_query, (exhibition_id,))
        registrations = cursor.fetchall()
        
        # print(f"找到 {len(registrations)} 条报名记录")
        
        # 格式化时间并获取参展人员
        for reg in registrations:
            # 格式化时间
            reg['registration_time'] = reg['registration_time'].strftime('%Y-%m-%d %H:%M:%S') if reg['registration_time'] else ''
            
            # 获取该报名的参展人员数量
            cursor.execute(
                "SELECT COUNT(*) as count FROM exhibition_participants WHERE registration_id = %s", 
                (reg['registration_id'],)
            )
            count_result = cursor.fetchone()
            reg['participant_count'] = count_result['count'] if count_result else 0
            
            # 获取该报名的参展人员详情
            cursor.execute("""
            SELECT id, name, phone, role, is_contact
            FROM exhibition_participants
            WHERE registration_id = %s
            ORDER BY is_contact DESC, id ASC
            """, (reg['registration_id'],))
            
            participants = cursor.fetchall()
            reg['participants'] = participants
            
            # 获取联系人信息
            contact_participant = None
            for participant in participants:
                if participant['is_contact']:
                    contact_participant = participant
                    break
            
            reg['contact_name'] = contact_participant['name'] if contact_participant else reg['publisher_name']
            reg['contact_phone'] = contact_participant['phone'] if contact_participant else ''
            
            # print(f"报名ID {reg['registration_id']} 有 {reg['participant_count']} 个参展人员")
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": registrations})
    except Exception as e:
        # print(f"获取参展人员失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"code": 1, "message": f"获取参展人员失败: {str(e)}"})

@admin_bp.route('/get_users', methods=['GET'])
def get_users():
    """
    获取用户列表
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        keyword: 搜索关键词
        role: 角色筛选
        school_id: 学校ID筛选
        publisher_id: 出版社ID筛选
        dealer_id: 经销商ID筛选
        sort_field: 排序字段 (name, role, phone, created_at, organization)
        sort_order: 排序方向 (asc, desc)
    返回:
        用户列表、角色统计、总数
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('page_size', 10))
        keyword = request.args.get('keyword', '')
        role_filter = request.args.get('role', 'all')
        school_id = request.args.get('school_id', '')
        publisher_id = request.args.get('publisher_id', '')
        dealer_id = request.args.get('dealer_id', '')
        sort_field = request.args.get('sort_field', '')
        sort_order = request.args.get('sort_order', 'desc')
        
        # 防止参数错误
        if page < 1:
            page = 1
        if limit < 1 or limit > 50:
            limit = 10
            
        offset = (page - 1) * limit
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建查询条件
        where_clauses = []
        params = []
        
        # 根据角色筛选
        if role_filter != 'all':
            where_clauses.append("role = %s")
            params.append(role_filter)
        
        # 关键词搜索
        if keyword:
            where_clauses.append("(u.username LIKE %s OR u.name LIKE %s OR u.phone_number LIKE %s)")
            search_param = f'%{keyword}%'
            params.extend([search_param, search_param, search_param])
        
        # 学校筛选
        if school_id:
            where_clauses.append("teacher_school_id = %s")
            params.append(school_id)
            
        # 出版社筛选
        if publisher_id:
            where_clauses.append("publisher_company_id = %s")
            params.append(publisher_id)
            
        # 经销商筛选
        if dealer_id:
            where_clauses.append("dealer_company_id = %s")
            params.append(dealer_id)
        
        # 构建WHERE子句
        where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"

        # 构建ORDER BY子句
        order_by_sql = "ORDER BY u.created_at DESC"  # 默认排序
        if sort_field and sort_order in ['asc', 'desc']:
            if sort_field == 'name':
                order_by_sql = f"ORDER BY u.name {sort_order}"
            elif sort_field == 'role':
                # 角色排序：admin < dealer < publisher < teacher
                role_order = "CASE u.role WHEN 'admin' THEN 1 WHEN 'dealer' THEN 2 WHEN 'publisher' THEN 3 WHEN 'teacher' THEN 4 ELSE 5 END"
                if sort_order == 'desc':
                    order_by_sql = f"ORDER BY {role_order} DESC"
                else:
                    order_by_sql = f"ORDER BY {role_order} ASC"
            elif sort_field == 'phone':
                order_by_sql = f"ORDER BY u.phone_number {sort_order}"
            elif sort_field == 'created_at':
                order_by_sql = f"ORDER BY u.created_at {sort_order}"
            elif sort_field == 'organization':
                # 所属机构排序：根据角色使用对应的机构名称
                organization_field = """
                CASE u.role
                    WHEN 'teacher' THEN s.name
                    WHEN 'publisher' THEN pc.name
                    WHEN 'dealer' THEN dc.name
                    ELSE '未设置'
                END
                """
                order_by_sql = f"ORDER BY {organization_field} {sort_order}"

        # 查询用户列表，包含关联的学校和公司信息
        query = f"""
        SELECT u.user_id as id, u.username, u.name, u.role, u.phone_number as phone, u.email, u.contact_info,
               u.teacher_school_id, u.publisher_company_id, u.dealer_company_id, u.created_at,
               s.name as school_name,
               pc.name as publisher_company_name,
               dc.name as dealer_company_name
        FROM users u
        LEFT JOIN schools s ON u.teacher_school_id = s.id
        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
        WHERE {where_sql}
        {order_by_sql}
        LIMIT %s, %s
        """
        
        params.append(offset)
        params.append(limit)
        
        cursor.execute(query, params)
        users = cursor.fetchall()
        
        # 格式化时间并处理数据
        for user in users:
            user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S') if user['created_at'] else ''

            # 设置公司名称（已通过JOIN查询获取）
            if user['role'] == 'teacher':
                user['company_name'] = user.get('school_name')
            elif user['role'] == 'publisher':
                user['company_name'] = user.get('publisher_company_name')
            elif user['role'] == 'dealer':
                user['company_name'] = user.get('dealer_company_name')
        
        # 查询总数
        count_params = params[:-2]  # 去掉 LIMIT 相关参数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM users u
        LEFT JOIN schools s ON u.teacher_school_id = s.id
        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
        WHERE {where_sql}
        """
        
        cursor.execute(count_query, count_params)
        total = cursor.fetchone()['total']
        
        # 查询各角色用户数量
        role_counts = {}
        
        # 全部
        cursor.execute("SELECT COUNT(*) as count FROM users")
        role_counts['all'] = cursor.fetchone()['count']
        
        # 管理员
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'admin'")
        role_counts['admin'] = cursor.fetchone()['count']
        
        # 教师
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'teacher'")
        role_counts['teacher'] = cursor.fetchone()['count']
        
        # 出版社
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'publisher'")
        role_counts['publisher'] = cursor.fetchone()['count']
        
        # 经销商
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'dealer'")
        role_counts['dealer'] = cursor.fetchone()['count']
        
        cursor.close()
        connection.close()
        
        result = {
            "users": users,
            "role_counts": role_counts,
            "total_count": total
        }
        
        return jsonify({"code": 0, "message": "获取成功", "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取用户列表失败: {str(e)}"})

@admin_bp.route('/get_user_detail', methods=['GET'])
def get_user_detail():
    """
    获取用户详情
    请求参数:
        user_id: 用户ID
    返回:
        用户详细信息
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({"code": 1, "message": "未提供用户ID"})
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询用户基本信息
        query = """
        SELECT user_id, username, name, role, phone_number, contact_info, email,
               teacher_school_id, publisher_company_id, dealer_company_id, created_at
        FROM users
        WHERE user_id = %s
        """
        cursor.execute(query, (user_id,))
        user = cursor.fetchone()
        
        if not user:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "用户不存在"})
        
        # 格式化时间
        user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S') if user['created_at'] else ''
        
        # 基于角色获取额外信息
        if user['role'] == 'teacher':
            # 获取学校信息和学校地址
            if user['teacher_school_id']:
                cursor.execute("SELECT id, name FROM schools WHERE id = %s", (user['teacher_school_id'],))
                school = cursor.fetchone()
                if school:
                    user['school'] = school
                    # 获取学校地址信息（优先获取主校区地址）
                    cursor.execute("""
                    SELECT id, campus_name, province, city, district, detailed_address,
                           is_main, postal_code, contact_phone
                    FROM school_addresses
                    WHERE school_id = %s
                    ORDER BY is_main DESC, id ASC
                    """, (user['teacher_school_id'],))
                    school_addresses = cursor.fetchall()
                    user['school']['addresses'] = school_addresses
                    # 设置主地址
                    if school_addresses:
                        user['school']['main_address'] = school_addresses[0]
                else:
                    user['school'] = {'id': None, 'name': '未知学校', 'addresses': [], 'main_address': None}
            else:
                user['school'] = {'id': None, 'name': '未知学校', 'addresses': [], 'main_address': None}

            # 获取教师的收货地址信息（这是教师个人的收货地址，与学校地址分开）
            cursor.execute("""
            SELECT address_id, name, phone_number, province, city, district, detailed_address
            FROM shipping_addresses
            WHERE teacher_id = %s
            """, (user_id,))
            shipping_addresses = cursor.fetchall()
            user['shipping_addresses'] = shipping_addresses

            # 获取教师的课程信息
            cursor.execute("""
            SELECT id, course_name, semester, course_type, student_count
            FROM teacher_courses
            WHERE teacher_id = %s
            """, (user_id,))
            courses = cursor.fetchall()
            user['courses'] = courses
            
        elif user['role'] == 'publisher':
            # 获取出版社公司信息和地址
            if user['publisher_company_id']:
                cursor.execute("""
                SELECT id, name, address, contact_phone, is_publisher
                FROM publisher_companies
                WHERE id = %s
                """, (user['publisher_company_id'],))
                company = cursor.fetchone()
                if company:
                    user['company'] = company
                    # 获取出版社地址信息（优先获取主办公地址）
                    cursor.execute("""
                    SELECT id, office_name, province, city, district, detailed_address,
                           is_main, postal_code, contact_phone
                    FROM publisher_addresses
                    WHERE publisher_company_id = %s
                    ORDER BY is_main DESC, id ASC
                    """, (user['publisher_company_id'],))
                    company_addresses = cursor.fetchall()
                    user['company']['addresses'] = company_addresses
                    # 设置主地址
                    if company_addresses:
                        user['company']['main_address'] = company_addresses[0]
                else:
                    user['company'] = {'id': None, 'name': '未知公司', 'addresses': [], 'main_address': None}

                # 获取公司权限
                cursor.execute("""
                SELECT can_recommend_books, can_register_exhibition
                FROM publisher_company_permissions
                WHERE company_id = %s
                """, (user['publisher_company_id'],))
                permissions = cursor.fetchone()
                user['permissions'] = permissions if permissions else {
                    'can_recommend_books': 0,
                    'can_register_exhibition': 0
                }
            else:
                user['company'] = {'id': None, 'name': '未知公司', 'addresses': [], 'main_address': None}
                user['permissions'] = {
                    'can_recommend_books': 0,
                    'can_register_exhibition': 0
                }
                
        elif user['role'] == 'dealer':
            # 获取经销商公司信息和地址
            if user['dealer_company_id']:
                cursor.execute("""
                SELECT id, name, address, contact_phone, parent_company_id
                FROM dealer_companies
                WHERE id = %s
                """, (user['dealer_company_id'],))
                company = cursor.fetchone()
                if company:
                    user['company'] = company
                    # 获取经销商地址信息（优先获取主办公地址）
                    cursor.execute("""
                    SELECT id, office_name, province, city, district, detailed_address,
                           is_main, postal_code, contact_phone
                    FROM dealer_addresses
                    WHERE dealer_company_id = %s
                    ORDER BY is_main DESC, id ASC
                    """, (user['dealer_company_id'],))
                    company_addresses = cursor.fetchall()
                    user['company']['addresses'] = company_addresses
                    # 设置主地址
                    if company_addresses:
                        user['company']['main_address'] = company_addresses[0]
                else:
                    user['company'] = {'id': None, 'name': '未知公司', 'addresses': [], 'main_address': None}

                # 获取公司权限
                cursor.execute("""
                SELECT can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition
                FROM dealer_company_permissions
                WHERE company_id = %s
                """, (user['dealer_company_id'],))
                permissions = cursor.fetchone()
                user['permissions'] = permissions if permissions else {
                    'can_recommend_books': 0,
                    'can_invite_users': 0,
                    'can_initiate_exhibition': 0,
                    'can_register_exhibition': 0
                }
            else:
                user['company'] = {'id': None, 'name': '未知公司', 'addresses': [], 'main_address': None}
                user['permissions'] = {
                    'can_recommend_books': 0,
                    'can_invite_users': 0,
                    'can_initiate_exhibition': 0,
                    'can_register_exhibition': 0
                }
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": user})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取用户详情失败: {str(e)}"})

@admin_bp.route('/add_user', methods=['POST'])
def add_user():
    """
    添加新用户
    请求数据:
        username: 用户名
        password: 密码
        name: 姓名
        phone_number: 手机号
        email: 邮箱
        role: 角色
        contact_info: 联系方式
        school_id: 学校ID(教师)
        publisher_company_id: 出版社ID(出版社)
        dealer_company_id: 经销商ID(经销商)
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        
        # 必填字段验证
        required_fields = ['username', 'password', 'name', 'phone_number', 'email', 'role']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({"code": 1, "message": f"缺少必填字段: {field}"})

        username = data['username']
        password = data['password']
        name = data['name']
        phone_number = data['phone_number']
        email = data['email']
        role = data['role']
        contact_info = data.get('contact_info', '')
        
        # 角色相关字段
        teacher_school_id = data.get('school_id', None) if role == 'teacher' else None
        publisher_company_id = data.get('publisher_company_id', None) if role == 'publisher' else None
        dealer_company_id = data.get('dealer_company_id', None) if role == 'dealer' else None
        
        if role not in ['admin', 'teacher', 'publisher', 'dealer']:
            return jsonify({"code": 1, "message": "无效的角色"})

        # 验证邮箱格式
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return jsonify({"code": 1, "message": "请输入正确的邮箱地址"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查用户名和电话号码是否已存在
        cursor.execute("SELECT username FROM users WHERE username = %s", (username,))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "用户名已存在"})
        
        cursor.execute("SELECT phone_number FROM users WHERE phone_number = %s", (phone_number,))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "手机号已存在"})
        
        # 检查邮箱是否已存在
        cursor.execute("SELECT email FROM users WHERE email = %s", (email,))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "邮箱已存在"})
        
        # 密码加密
        hashed_password = generate_password_hash(password)
        
        # 创建新用户
        insert_query = """
        INSERT INTO users (username, password, name, phone_number, email, role, contact_info, 
                          teacher_school_id, publisher_company_id, dealer_company_id, created_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
        """
        
        cursor.execute(insert_query, (
            username, hashed_password, name, phone_number, email, role, contact_info,
            teacher_school_id, publisher_company_id, dealer_company_id
        ))
        
        # 获取新创建的用户ID
        user_id = cursor.lastrowid

        # 如果是出版社用户，创建默认目录
        if role == 'publisher':
            try:
                cursor.execute("""
                    INSERT INTO directories (name, parent_id, publisher_id)
                    VALUES (%s, %s, %s)
                """, ('默认目录', None, user_id))
                print(f"为出版社用户 {user_id} 创建默认目录成功")
            except Exception as e:
                print(f"为出版社用户 {user_id} 创建默认目录失败: {str(e)}")
                # 目录创建失败不影响用户创建成功

        connection.commit()

        # 记录创建用户日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.USER_CREATE,
            description=f"管理员创建新用户：{username}",
            target_type='user',
            target_id=user_id,
            details={
                'username': username,
                'role': role,
                'name': name,
                'created_by_admin': True,
                'has_default_directory': role == 'publisher'
            }
        )

        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "用户创建成功", "user_id": user_id})
    except Exception as e:
        # 记录创建用户失败日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.USER_CREATE,
            result=AuditLogService.Result.FAILURE,
            description="管理员创建用户失败",
            details={
                'attempted_username': data.get('username') if 'data' in locals() else '',
                'attempted_role': data.get('role') if 'data' in locals() else '',
                'error_reason': str(e)
            }
        )
        return jsonify({"code": 1, "message": f"创建用户失败: {str(e)}"})

@admin_bp.route('/edit_user', methods=['POST'])
def edit_user():
    """
    编辑用户信息
    请求数据:
        user_id: 用户ID
        username: 用户名
        name: 姓名
        phone_number: 手机号
        email: 邮箱
        role: 角色
        contact_info: 联系方式
        school_id: 学校ID(教师)
        publisher_company_id: 出版社ID(出版社)
        dealer_company_id: 经销商ID(经销商)
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        
        # 必填字段验证
        required_fields = ['user_id', 'username', 'name', 'phone_number', 'email', 'role']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({"code": 1, "message": f"缺少必填字段: {field}"})

        user_id = data['user_id']
        username = data['username']
        name = data['name']
        phone_number = data['phone_number']
        email = data['email']
        role = data['role']
        contact_info = data.get('contact_info', '')
        
        # 角色相关字段
        teacher_school_id = data.get('school_id', None) if role == 'teacher' else None
        publisher_company_id = data.get('publisher_company_id', None) if role == 'publisher' else None
        dealer_company_id = data.get('dealer_company_id', None) if role == 'dealer' else None
        
        if role not in ['admin', 'teacher', 'publisher', 'dealer']:
            return jsonify({"code": 1, "message": "无效的角色"})

        # 验证邮箱格式
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return jsonify({"code": 1, "message": "请输入正确的邮箱地址"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查用户是否存在
        cursor.execute("SELECT username, phone_number, email FROM users WHERE user_id = %s", (user_id,))
        user = cursor.fetchone()
        if not user:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "用户不存在"})
        
        # 检查用户名是否已被其他用户使用
        if username != user['username']:
            cursor.execute("SELECT user_id FROM users WHERE username = %s AND user_id != %s", (username, user_id))
            if cursor.fetchone():
                cursor.close()
                connection.close()
                return jsonify({"code": 1, "message": "用户名已被其他用户使用"})
        
        # 检查电话号码是否已被其他用户使用
        if phone_number != user['phone_number']:
            cursor.execute("SELECT user_id FROM users WHERE phone_number = %s AND user_id != %s", (phone_number, user_id))
            if cursor.fetchone():
                cursor.close()
                connection.close()
                return jsonify({"code": 1, "message": "手机号已被其他用户使用"})
        
        # 检查邮箱是否已被其他用户使用
        if email != user.get('email', ''):
            cursor.execute("SELECT user_id FROM users WHERE email = %s AND user_id != %s", (email, user_id))
            if cursor.fetchone():
                cursor.close()
                connection.close()
                return jsonify({"code": 1, "message": "邮箱已被其他用户使用"})
        
        # 更新用户信息
        update_query = """
        UPDATE users 
        SET username = %s, name = %s, phone_number = %s, email = %s, role = %s, contact_info = %s,
            teacher_school_id = %s, publisher_company_id = %s, dealer_company_id = %s
        WHERE user_id = %s
        """
        
        cursor.execute(update_query, (
            username, name, phone_number, email, role, contact_info,
            teacher_school_id, publisher_company_id, dealer_company_id, user_id
        ))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "用户信息更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新用户信息失败: {str(e)}"})

@admin_bp.route('/reset_password', methods=['POST'])
def reset_password():
    """
    重置用户密码
    请求数据:
        user_id: 用户ID
        new_password: 新密码
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        
        # 必填字段验证
        if 'user_id' not in data or not data['user_id']:
            return jsonify({"code": 1, "message": "未提供用户ID"})
        if 'new_password' not in data or not data['new_password']:
            return jsonify({"code": 1, "message": "未提供新密码"})
        
        user_id = data['user_id']
        new_password = data['new_password']
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查用户是否存在
        cursor.execute("SELECT user_id FROM users WHERE user_id = %s", (user_id,))
        if not cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "用户不存在"})
        
        # 密码加密
        hashed_password = generate_password_hash(new_password)
        
        # 更新密码
        cursor.execute("UPDATE users SET password = %s WHERE user_id = %s", (hashed_password, user_id))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "密码重置成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"重置密码失败: {str(e)}"})

@admin_bp.route('/delete_user', methods=['POST'])
def delete_user():
    """
    删除用户
    请求数据:
        user_id: 用户ID
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        data = request.get_json()
        
        if 'user_id' not in data or not data['user_id']:
            return jsonify({"code": 1, "message": "未提供用户ID"})
        
        user_id = data['user_id']
        
        # 防止删除当前登录的管理员账户
        if int(user_id) == int(session.get('user_id')):
            return jsonify({"code": 1, "message": "不能删除当前登录的账户"})
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查用户是否存在
        cursor.execute("SELECT role FROM users WHERE user_id = %s", (user_id,))
        user = cursor.fetchone()
        if not user:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "用户不存在"})
        
        # 开始事务
        connection.begin()
        
        try:
            # 根据用户角色删除相关数据
            role = user['role']
            
            if role == 'teacher':
                # 删除教师相关数据
                cursor.execute("DELETE FROM shipping_addresses WHERE teacher_id = %s", (user_id,))
                cursor.execute("DELETE FROM teacher_courses WHERE teacher_id = %s", (user_id,))
                cursor.execute("DELETE FROM sample_requests WHERE teacher_id = %s", (user_id,))
                
            elif role == 'publisher':
                # 删除出版社相关数据
                cursor.execute("DELETE FROM directories WHERE publisher_id = %s", (user_id,))
                # 删除样书可能会级联删除很多数据，需要谨慎处理
                
            elif role == 'dealer':
                # 删除经销商相关数据
                cursor.execute("DELETE FROM promotion_reports WHERE dealer_id = %s", (user_id,))
                cursor.execute("DELETE FROM invitation_codes WHERE inviter_id = %s", (user_id,))
            
            # 最后删除用户
            cursor.execute("DELETE FROM users WHERE user_id = %s", (user_id,))
            
            # 提交事务
            connection.commit()

            # 记录删除用户日志
            AuditLogService.log_action(
                action_type=AuditLogService.ActionType.USER_DELETE,
                description=f"管理员删除用户，用户ID：{user_id}",
                target_type='user',
                target_id=user_id,
                details={
                    'deleted_user_id': user_id,
                    'deleted_user_role': role
                }
            )

            return jsonify({"code": 0, "message": "用户删除成功"})
        except Exception as e:
            # 回滚事务
            connection.rollback()
            raise e
        finally:
            cursor.close()
            connection.close()
    except Exception as e:
        # 记录删除用户失败日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.USER_DELETE,
            result=AuditLogService.Result.FAILURE,
            description="管理员删除用户失败",
            target_type='user',
            target_id=data.get('user_id') if 'data' in locals() else None,
            details={
                'attempted_user_id': data.get('user_id') if 'data' in locals() else None,
                'error_reason': str(e)
            }
        )
        return jsonify({"code": 1, "message": f"删除用户失败: {str(e)}"})

# ==================== 日志查看相关接口 ====================

@admin_bp.route('/get_audit_logs', methods=['GET'])
def get_audit_logs():
    """
    获取审计日志列表
    请求参数:
        page: 页码，默认1
        page_size: 每页数量，默认20
        user_id: 用户ID筛选
        action_type: 操作类型筛选
        result: 操作结果筛选
        target_type: 目标类型筛选
        target_id: 目标ID筛选
        start_date: 开始时间
        end_date: 结束时间
        search_keyword: 搜索关键词
        order_by: 排序字段，默认created_at
        order_direction: 排序方向，默认DESC
    返回:
        日志列表、总数、分页信息
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        user_id = request.args.get('user_id', '')
        username = request.args.get('username', '')
        action_type = request.args.get('action_type', '')
        result = request.args.get('result', '')
        target_type = request.args.get('target_type', '')
        target_id = request.args.get('target_id', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        search_keyword = request.args.get('search_keyword', '')
        order_by = request.args.get('order_by', 'created_at')
        order_direction = request.args.get('order_direction', 'DESC')

        # 参数验证
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20
        if order_direction not in ['ASC', 'DESC']:
            order_direction = 'DESC'
        if order_by not in ['created_at', 'action_type', 'result', 'user_id']:
            order_by = 'created_at'

        # 转换日期参数
        start_date_obj = None
        end_date_obj = None
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            except:
                pass
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
            except:
                pass

        # 检查是否为导出请求
        export_request = request.args.get('export', '').lower() == 'true'

        if export_request:
            # 导出时获取更多数据，不分页
            page = 1
            page_size = 10000  # 限制导出数量

        # 调用日志服务
        result_data = AuditLogService.get_logs(
            user_id=int(user_id) if user_id else None,
            username=username if username else None,
            action_type=action_type if action_type else None,
            result=result if result else None,
            target_type=target_type if target_type else None,
            target_id=target_id if target_id else None,
            start_date=start_date_obj,
            end_date=end_date_obj,
            search_keyword=search_keyword if search_keyword else None,
            page=page,
            page_size=page_size,
            order_by=order_by,
            order_direction=order_direction
        )

        if result_data['code'] == 0:
            if export_request:
                # 返回CSV格式的数据
                return export_logs_to_csv(result_data['data']['logs'])
            else:
                return jsonify({
                    "code": 0,
                    "message": "获取成功",
                    "data": result_data['data']
                })
        else:
            return jsonify({
                "code": 1,
                "message": result_data['message']
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取日志失败: {str(e)}"})

@admin_bp.route('/get_audit_log_statistics', methods=['GET'])
def get_audit_log_statistics():
    """
    获取审计日志统计信息
    请求参数:
        user_id: 用户ID筛选
        start_date: 开始时间
        end_date: 结束时间
        group_by: 分组字段（action_type, result, user_id, date）
    返回:
        统计结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 获取请求参数
        user_id = request.args.get('user_id', '')
        username = request.args.get('username', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        group_by = request.args.get('group_by', 'action_type')

        # 参数验证
        if group_by not in ['action_type', 'result', 'user_id', 'date']:
            group_by = 'action_type'

        # 转换日期参数
        start_date_obj = None
        end_date_obj = None
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            except:
                pass
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
            except:
                pass

        # 调用日志服务
        result_data = AuditLogService.get_statistics(
            user_id=int(user_id) if user_id else None,
            username=username if username else None,
            start_date=start_date_obj,
            end_date=end_date_obj,
            group_by=group_by
        )

        if result_data['code'] == 0:
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": result_data['data']
            })
        else:
            return jsonify({
                "code": 1,
                "message": result_data['message']
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取统计失败: {str(e)}"})

@admin_bp.route('/clean_audit_logs', methods=['POST'])
def clean_audit_logs():
    """
    清理旧的审计日志
    请求数据:
        days: 保留天数，默认90天
    返回:
        清理结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        days = int(data.get('days', 90)) if data else 90

        # 参数验证
        if days < 1 or days > 365:
            return jsonify({"code": 1, "message": "保留天数必须在1-365之间"})

        # 调用日志服务
        result_data = AuditLogService.clean_old_logs(days=days)

        if result_data['code'] == 0:
            # 记录清理操作日志
            AuditLogService.log_action(
                action_type=AuditLogService.ActionType.SYSTEM_CONFIG_UPDATE,
                description=f"管理员清理审计日志，保留{days}天",
                details={
                    'days': days,
                    'deleted_count': result_data['data']['deleted_count']
                }
            )

            return jsonify({
                "code": 0,
                "message": result_data['message'],
                "data": result_data['data']
            })
        else:
            return jsonify({
                "code": 1,
                "message": result_data['message']
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"清理日志失败: {str(e)}"})

@admin_bp.route('/get_audit_log_options', methods=['GET'])
def get_audit_log_options():
    """
    获取审计日志筛选选项
    返回:
        操作类型、结果类型、目标类型等选项
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 操作类型选项
        action_types = [
            {'value': 'login', 'label': '用户登录'},
            {'value': 'logout', 'label': '用户注销'},
            {'value': 'register', 'label': '用户注册'},
            {'value': 'sample_request', 'label': '申请样书'},
            {'value': 'sample_approve', 'label': '批准样书申请'},
            {'value': 'sample_reject', 'label': '拒绝样书申请'},
            {'value': 'sample_revoke', 'label': '撤销样书申请处理'},
            {'value': 'sample_upload', 'label': '上传样书'},
            {'value': 'sample_update', 'label': '更新样书信息'},
            {'value': 'sample_delete', 'label': '删除样书'},
            {'value': 'order_create', 'label': '创建订单'},
            {'value': 'order_update', 'label': '更新订单'},
            {'value': 'order_process', 'label': '处理订单'},
            {'value': 'order_revoke', 'label': '撤销订单处理'},
            {'value': 'order_upload', 'label': '上传订单'},
            {'value': 'order_delete', 'label': '删除订单'},
            {'value': 'recommendation_create', 'label': '发起推荐'},
            {'value': 'recommendation_approve', 'label': '批准推荐'},
            {'value': 'recommendation_reject', 'label': '拒绝推荐'},
            {'value': 'recommendation_update', 'label': '更新推荐'},
            {'value': 'user_create', 'label': '创建用户'},
            {'value': 'user_update', 'label': '更新用户信息'},
            {'value': 'user_delete', 'label': '删除用户'},
            {'value': 'user_permission_change', 'label': '权限变更'},
            {'value': 'user_role_switch', 'label': '角色切换'},
            {'value': 'system_config_update', 'label': '系统配置更新'},
            {'value': 'directory_create', 'label': '创建目录'},
            {'value': 'directory_update', 'label': '更新目录'},
            {'value': 'directory_delete', 'label': '删除目录'},
            {'value': 'file_upload', 'label': '文件上传'},
            {'value': 'file_delete', 'label': '文件删除'},
            {'value': 'data_import', 'label': '数据导入'},
            {'value': 'data_export', 'label': '数据导出'}
        ]

        # 操作结果选项
        result_types = [
            {'value': 'success', 'label': '成功'},
            {'value': 'failure', 'label': '失败'},
            {'value': 'partial', 'label': '部分成功'}
        ]

        # 目标类型选项
        target_types = [
            {'value': 'user', 'label': '用户'},
            {'value': 'sample_book', 'label': '样书'},
            {'value': 'sample_request', 'label': '样书申请'},
            {'value': 'order', 'label': '订单'},
            {'value': 'recommendation', 'label': '推荐'},
            {'value': 'promotion_report', 'label': '报备'},
            {'value': 'directory', 'label': '目录'},
            {'value': 'file', 'label': '文件'},
            {'value': 'import_file', 'label': '导入文件'},
            {'value': 'export_file', 'label': '导出文件'},
            {'value': 'system_config', 'label': '系统配置'}
        ]

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "action_types": action_types,
                "result_types": result_types,
                "target_types": target_types
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取选项失败: {str(e)}"})

def export_logs_to_csv(logs):
    """
    将日志数据导出为CSV格式
    """
    try:
        import csv
        from io import StringIO
        from flask import make_response

        # 创建CSV内容
        output = StringIO()
        writer = csv.writer(output)

        # 写入表头
        headers = [
            '日志ID', '操作时间', '用户ID', '用户名', '用户角色',
            '操作类型', '操作结果', '操作描述', '目标类型', '目标ID',
            'IP地址', '用户代理', '详细信息'
        ]
        writer.writerow(headers)

        # 写入数据
        for log in logs:
            # 获取操作类型描述
            action_type_desc = AuditLogService.get_action_type_description(log.get('action_type', ''))
            result_desc = AuditLogService.get_result_description(log.get('result', ''))
            user_role_desc = AuditLogService.get_user_role_description(log.get('user_role', ''))

            # 处理详细信息
            details_str = ''
            if log.get('details'):
                try:
                    import json
                    details_str = json.dumps(log['details'], ensure_ascii=False)
                except:
                    details_str = str(log['details'])

            row = [
                log.get('id', ''),
                log.get('created_at', ''),
                log.get('user_id', ''),
                log.get('username', ''),
                user_role_desc,
                action_type_desc,
                result_desc,
                log.get('description', ''),
                log.get('target_type', ''),
                log.get('target_id', ''),
                log.get('ip_address', ''),
                log.get('user_agent', ''),
                details_str
            ]
            writer.writerow(row)

        # 创建响应
        output.seek(0)
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=audit_logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        # 记录导出操作日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.DATA_EXPORT,
            description=f"管理员导出审计日志",
            details={
                'export_count': len(logs),
                'export_format': 'csv'
            }
        )

        return response

    except Exception as e:
        return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})

@admin_bp.route('/get_schools', methods=['GET'])
def get_schools():
    """获取所有学校列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        query = "SELECT id, name FROM schools ORDER BY name"
        cursor.execute(query)
        schools = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": schools})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取学校列表失败: {str(e)}"})

@admin_bp.route('/get_companies', methods=['GET'])
def get_companies():
    """
    获取公司列表，用于用户编辑表单
    请求参数:
        type: 公司类型 (publisher/dealer)
        search: 搜索关键词
    返回:
        公司列表
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        company_type = request.args.get('type', 'publisher')
        search = request.args.get('search', '')
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询公司
        if company_type == 'publisher':
            if search:
                cursor.execute("SELECT id, name FROM publisher_companies WHERE name LIKE %s ORDER BY name", (f'%{search}%',))
            else:
                cursor.execute("SELECT id, name FROM publisher_companies ORDER BY name")
        else:  # dealer
            if search:
                cursor.execute("SELECT id, name FROM dealer_companies WHERE name LIKE %s ORDER BY name", (f'%{search}%',))
            else:
                cursor.execute("SELECT id, name FROM dealer_companies ORDER BY name")
                
        companies = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": companies})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取公司列表失败: {str(e)}"})

@admin_bp.route('/get_publishers', methods=['GET'])
def get_publishers():
    """获取所有出版社列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        query = "SELECT id, name FROM publisher_companies ORDER BY name"
        cursor.execute(query)
        publishers = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": publishers})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})

@admin_bp.route('/get_dealers', methods=['GET'])
def get_dealers():
    """获取所有经销商列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        query = "SELECT id, name FROM dealer_companies ORDER BY name"
        cursor.execute(query)
        dealers = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": dealers})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取经销商列表失败: {str(e)}"})

@admin_bp.route('/admin_manage_users', methods=['GET'])
def admin_manage_users():
    """
    渲染用户管理页面
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    return render_template('pc_admin_manage_users.html')

@admin_bp.route('/get_users_schools', methods=['GET'])
def get_users_schools():
    """
    获取有用户的学校列表，用于用户筛选
    返回:
        学校列表 (id, name)
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询有用户的学校
        query = """
        SELECT DISTINCT s.id, s.name 
        FROM schools s
        JOIN users u ON s.id = u.teacher_school_id
        WHERE u.teacher_school_id IS NOT NULL
        ORDER BY s.name
        """
        cursor.execute(query)
        schools = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": schools})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取学校列表失败: {str(e)}"})

@admin_bp.route('/get_users_companies', methods=['GET'])
def get_users_companies():
    """
    获取有用户的公司列表，用于用户筛选
    返回:
        公司列表 (id, name, type)
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 查询有出版社用户的公司
        publisher_query = """
        SELECT DISTINCT pc.id, pc.name, 'publisher' as type
        FROM publisher_companies pc
        JOIN users u ON pc.id = u.publisher_company_id
        WHERE u.publisher_company_id IS NOT NULL
        """
        cursor.execute(publisher_query)
        publishers = cursor.fetchall()
        
        # 查询有经销商用户的公司
        dealer_query = """
        SELECT DISTINCT dc.id, dc.name, 'dealer' as type
        FROM dealer_companies dc
        JOIN users u ON dc.id = u.dealer_company_id
        WHERE u.dealer_company_id IS NOT NULL
        """
        cursor.execute(dealer_query)
        dealers = cursor.fetchall()
        
        # 合并结果
        companies = publishers + dealers
        
        # 按名称排序
        companies.sort(key=lambda x: x['name'])
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": companies})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取公司列表失败: {str(e)}"})

# 邮件配置管理相关接口
@admin_bp.route('/get_email_configs', methods=['GET'])
def get_email_configs():
    """
    获取邮件配置列表
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        keyword: 搜索关键词
        status: 状态筛选 (all/active/inactive)
    返回:
        邮件配置列表、状态统计、总数
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        keyword = request.args.get('keyword', '')
        status = request.args.get('status', 'all')

        # 防止参数错误
        if page < 1:
            page = 1
        if limit < 1 or limit > 50:
            limit = 10

        offset = (page - 1) * limit

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 构建查询条件
        where_clauses = []
        params = []

        # 根据状态筛选
        if status == 'active':
            where_clauses.append("is_active = 1")
        elif status == 'inactive':
            where_clauses.append("is_active = 0")

        # 关键词搜索
        if keyword:
            where_clauses.append("(from_email LIKE %s OR from_name LIKE %s OR smtp_host LIKE %s)")
            search_param = f'%{keyword}%'
            params.extend([search_param, search_param, search_param])

        # 构建WHERE子句
        where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"

        # 查询邮件配置列表
        query = f"""
        SELECT id, smtp_host, smtp_port, smtp_username, from_email, from_name,
               use_tls, use_ssl, is_active, created_at, updated_at
        FROM email_config
        WHERE {where_sql}
        ORDER BY created_at DESC
        LIMIT %s, %s
        """

        params.append(offset)
        params.append(limit)

        cursor.execute(query, params)
        configs = cursor.fetchall()

        # 格式化时间并隐藏敏感信息
        for config in configs:
            config['created_at'] = config['created_at'].strftime('%Y-%m-%d %H:%M:%S') if config['created_at'] else ''
            config['updated_at'] = config['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if config['updated_at'] else ''
            # 隐藏密码，只显示前几位
            config['smtp_password_masked'] = config.get('smtp_password', '')[:3] + '***' if config.get('smtp_password') else ''
            # 移除敏感字段
            if 'smtp_password' in config:
                del config['smtp_password']

        # 查询总数
        count_params = params[:-2]  # 去掉 LIMIT 相关参数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM email_config
        WHERE {where_sql}
        """

        cursor.execute(count_query, count_params)
        total = cursor.fetchone()['total']

        # 查询各状态配置数量
        status_counts = {}

        # 全部
        cursor.execute("SELECT COUNT(*) as count FROM email_config")
        status_counts['all'] = cursor.fetchone()['count']

        # 启用
        cursor.execute("SELECT COUNT(*) as count FROM email_config WHERE is_active = 1")
        status_counts['active'] = cursor.fetchone()['count']

        # 禁用
        cursor.execute("SELECT COUNT(*) as count FROM email_config WHERE is_active = 0")
        status_counts['inactive'] = cursor.fetchone()['count']

        cursor.close()
        connection.close()

        result = {
            "configs": configs,
            "status_counts": status_counts,
            "total": total
        }

        return jsonify({"code": 0, "message": "获取成功", "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取邮件配置列表失败: {str(e)}"})

@admin_bp.route('/get_email_config_detail', methods=['GET'])
def get_email_config_detail():
    """
    获取邮件配置详情
    请求参数:
        id: 配置ID
    返回:
        邮件配置详细信息
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        config_id = request.args.get('id')
        if not config_id:
            return jsonify({"code": 1, "message": "未提供配置ID"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询配置详情
        query = """
        SELECT id, smtp_host, smtp_port, smtp_username, smtp_password, from_email, from_name,
               use_tls, use_ssl, is_active, created_at, updated_at
        FROM email_config
        WHERE id = %s
        """
        cursor.execute(query, (config_id,))
        config = cursor.fetchone()

        if not config:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "邮件配置不存在"})

        # 格式化时间
        config['created_at'] = config['created_at'].strftime('%Y-%m-%d %H:%M:%S') if config['created_at'] else ''
        config['updated_at'] = config['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if config['updated_at'] else ''

        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "获取成功", "data": config})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取邮件配置详情失败: {str(e)}"})

@admin_bp.route('/download_user_import_template', methods=['GET'])
def download_user_import_template():
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取应用根目录
        from flask import current_app
        app_root = current_app.root_path
        
        # 确保static/excel目录存在
        excel_dir = os.path.join(app_root, 'static', 'excel')
        os.makedirs(excel_dir, exist_ok=True)
        
        # 定义模板文件路径
        template_path = os.path.join(excel_dir, 'user_import_template.xlsx')
        
        # 创建一个样例数据的DataFrame
        data = {
            '用户名*': ['teacher1', 'publisher1', 'dealer1', 'admin1'],
            '密码*': ['password123', 'password123', 'password123', 'password123'],
            '姓名': ['张老师', '李编辑', '王经理', '管理员'],
            '角色*': ['teacher', 'publisher', 'dealer', 'admin'],
            '所在学校/单位名称*': ['示例学校', '示例出版社', '示例经销商', ''],
            '手机号': ['13800000001', '13800000002', '13800000003', '13800000004'],
            '邮箱': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        }
        df = pd.DataFrame(data)
        
        # 创建一个Excel writer对象
        writer = pd.ExcelWriter(template_path, engine='xlsxwriter')
        
        # 将数据写入Excel
        df.to_excel(writer, sheet_name='用户数据', index=False)
        
        # 获取xlsxwriter工作簿和工作表对象
        workbook = writer.book
        worksheet = writer.sheets['用户数据']
        
        # 定义格式
        required_format = workbook.add_format({'bold': True, 'bg_color': '#FFD700'})
        optional_format = workbook.add_format({'bg_color': '#E0E0E0'})
        header_format = workbook.add_format({'bold': True, 'bg_color': '#4F81BD', 'font_color': 'white'})
        
        # 设置列宽
        worksheet.set_column('A:A', 15)  # 用户名
        worksheet.set_column('B:B', 15)  # 密码
        worksheet.set_column('C:C', 15)  # 姓名
        worksheet.set_column('D:D', 10)  # 角色
        worksheet.set_column('E:E', 20)  # 所在学校/单位名称
        worksheet.set_column('F:F', 15)  # 手机号
        worksheet.set_column('G:G', 20)  # 邮箱
        
        # 设置表头格式
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # 标记必填字段
        for row in range(1, len(df) + 1):
            worksheet.write(row, 0, df.iloc[row-1, 0], required_format)  # 用户名
            worksheet.write(row, 1, df.iloc[row-1, 1], required_format)  # 密码
            worksheet.write(row, 3, df.iloc[row-1, 3], required_format)  # 角色
            
            # 所在学校/单位对于教师、出版社、经销商是必填的
            role = df.iloc[row-1, 3]
            if role in ['teacher', 'publisher', 'dealer']:
                worksheet.write(row, 4, df.iloc[row-1, 4], required_format)
            else:
                worksheet.write(row, 4, df.iloc[row-1, 4], optional_format)
            
            # 其他字段是可选的
            worksheet.write(row, 2, df.iloc[row-1, 2], optional_format)  # 姓名
            worksheet.write(row, 5, df.iloc[row-1, 5], optional_format)  # 手机号
            worksheet.write(row, 6, df.iloc[row-1, 6], optional_format)  # 邮箱
        
        # 添加一个说明页
        instruction_sheet = workbook.add_worksheet('填写说明')
        instruction_sheet.set_column('A:A', 100)
        
        instructions = [
            "用户导入模板使用说明：",
            "1. 必填字段（黄色背景）：用户名*（用户名）、密码*（密码）、角色*（角色）",
            "2. 对于角色为teacher、publisher、dealer的用户，所在学校/单位名称*（所属机构）也是必填的",
            "3. 可选字段（灰色背景）：姓名（姓名）、手机号（手机号）、邮箱（电子邮箱）",
            "4. 角色*字段只能填写以下值之一：admin（管理员）、teacher（教师）、publisher（出版社）、dealer（经销商）",
            "5. 所在学校/单位名称*必须填写已存在的学校、出版社或经销商名称",
            "6. 手机号格式必须为11位手机号",
            "7. 邮箱格式必须为有效的电子邮箱地址",
            "8. 请不要修改表头和格式"
        ]
        
        for i, instruction in enumerate(instructions):
            instruction_sheet.write(i, 0, instruction)
        
        # 保存Excel文件
        writer.close()
        
        # 发送文件
        return send_file(
            template_path,
            as_attachment=True,
            download_name='user_import_template.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"生成模板失败: {str(e)}"})

@admin_bp.route('/import_users', methods=['POST'])
def import_users():
    """
    批量导入用户
    返回:
        导入结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    if 'file' not in request.files:
        return jsonify({"code": 1, "message": "未上传文件"})
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({"code": 1, "message": "未选择文件"})
    
    if not file.filename.endswith(('.xls', '.xlsx')):
        return jsonify({"code": 1, "message": "文件格式不正确，请上传Excel文件"})
    
    try:
        # print(f"开始处理上传的文件: {file.filename}")
        
        # 确保上传目录存在
        from flask import current_app
        app_root = current_app.root_path
        upload_dir = os.path.join(app_root, 'static', 'excel', 'uploads')
        os.makedirs(upload_dir, exist_ok=True)
        # print(f"上传目录: {upload_dir}")
        
        # 生成唯一的文件名
        filename = f"user_import_{int(time.time())}_{os.urandom(4).hex()}.xlsx"
        upload_path = os.path.join(upload_dir, filename)
        # print(f"保存文件路径: {upload_path}")
        
        # 保存上传的文件
        file.save(upload_path)
        # print(f"文件已保存到: {upload_path}")
        
        # 读取Excel文件
        try:
            df = pd.read_excel(upload_path)
            # print(f"成功读取Excel文件，共有 {len(df)} 行数据")
        except Exception as excel_err:
            # print(f"读取Excel文件失败: {str(excel_err)}")
            return jsonify({"code": 1, "message": f"读取Excel文件失败: {str(excel_err)}"})
        
        # 验证必要的列是否存在
        required_columns = ['用户名*', '角色*', '所在学校/单位名称*', '密码*']
        # print(f"Excel文件列: {df.columns.tolist()}")
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            return jsonify({
                "code": 1, 
                "message": f"导入失败，缺少必要的列: {', '.join(missing_columns)}"
            })
        
        # 限制记录数量
        if len(df) > 500:
            return jsonify({
                "code": 1, 
                "message": "单次导入最多支持500条记录，请分批导入"
            })
        
        if len(df) == 0:
            return jsonify({
                "code": 1, 
                "message": "导入文件不包含任何数据"
            })
        
        # 重命名列以匹配数据库字段
        df = df.rename(columns={
            '用户名*': 'username',
            '姓名': 'name',
            '角色*': 'role',
            '所在学校/单位名称*': 'institution',
            '手机号': 'phone_number',
            '密码*': 'password',
            '邮箱': 'email'
        })
        # print("列重命名完成")
        
        # 连接数据库 - 使用pymysql直接连接而不是使用DictCursor
        try:
            # 导入所需模块
            import pymysql
            from app.config import Config
            
            # 直接连接数据库
            conn = pymysql.connect(
                host=Config.DB_HOST,
                user=Config.DB_USER,
                password=Config.DB_PASSWORD,
                database=Config.DB_NAME
            )
            cursor = conn.cursor()
            # print("数据库连接成功")
            
        except Exception as db_err:
            import traceback
            traceback_info = traceback.format_exc()
            error_msg = str(db_err) if str(db_err) else "数据库连接失败，未返回具体错误信息"
            # print(f"数据库连接失败: {error_msg}")
            # print(f"错误详情: {traceback_info}")
            return jsonify({"code": 1, "message": f"数据库连接失败: {error_msg}"})
        
        # 准备结果数据
        success_count = 0
        error_records = []
        
        try:
            # 获取所有现有用户名
            cursor.execute("SELECT username FROM users")
            existing_usernames = {row[0] for row in cursor.fetchall()}
            
            # 获取所有现有手机号
            cursor.execute("SELECT phone_number FROM users WHERE phone_number IS NOT NULL AND phone_number != ''")
            existing_phones = {row[0] for row in cursor.fetchall()}
            
            # 获取所有现有邮箱
            cursor.execute("SELECT email FROM users WHERE email IS NOT NULL AND email != ''")
            existing_emails = {row[0] for row in cursor.fetchall()}
            
            # 获取所有学校
            cursor.execute("SELECT id, name FROM schools")
            schools_data = cursor.fetchall()
            schools = {row[1]: row[0] for row in schools_data}
            
            # 获取所有出版社
            cursor.execute("SELECT id, name FROM publisher_companies")
            publisher_data = cursor.fetchall()
            publishers = {row[1]: row[0] for row in publisher_data}
            
            # 获取所有经销商
            cursor.execute("SELECT id, name FROM dealer_companies")
            dealer_data = cursor.fetchall()
            dealers = {row[1]: row[0] for row in dealer_data}
            
            # 开始事务
            conn.begin()
            
            # 处理每条记录
            for index, row in df.iterrows():
                record_num = index + 2  # Excel行号（考虑标题行）
                errors = []
                
                # 验证必填字段
                if pd.isna(row['username']) or not row['username']:
                    errors.append("用户名不能为空")
                elif row['username'] in existing_usernames:
                    errors.append(f"用户名 '{row['username']}' 已存在")
                
                if pd.isna(row['role']) or not row['role']:
                    errors.append("角色不能为空")
                elif row['role'] not in ['admin', 'teacher', 'publisher', 'dealer']:
                    errors.append(f"角色 '{row['role']}' 无效，必须是 admin, teacher, publisher 或 dealer")
                
                if pd.isna(row['password']) or not row['password']:
                    errors.append("密码不能为空")
                
                # 验证手机号（如果提供）
                phone_number = str(row['phone_number']) if not pd.isna(row['phone_number']) and row['phone_number'] else None
                if phone_number:
                    if not re.match(r'^1[3-9]\d{9}$', phone_number):
                        errors.append(f"手机号 '{phone_number}' 格式不正确")
                    elif phone_number in existing_phones:
                        errors.append(f"手机号 '{phone_number}' 已被使用")
                
                # 验证邮箱（如果提供）
                email = str(row['email']) if not pd.isna(row['email']) and row['email'] else None
                if email:
                    if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                        errors.append(f"邮箱 '{email}' 格式不正确")
                    elif email in existing_emails:
                        errors.append(f"邮箱 '{email}' 已被使用")
                
                # 根据角色设置相应的机构ID
                teacher_school_id = None
                publisher_company_id = None
                dealer_company_id = None
                
                if row['role'] == 'admin':
                    # 管理员不需要关联机构
                    pass
                
                elif row['role'] == 'teacher':
                    # 检查学校
                    if pd.isna(row['institution']) or not row['institution']:
                        errors.append("教师必须指定所在学校")
                    elif str(row['institution']) not in schools:
                        errors.append(f"找不到名为 '{row['institution']}' 的学校")
                    else:
                        teacher_school_id = schools[str(row['institution'])]
                
                elif row['role'] == 'publisher':
                    # 检查出版社
                    if pd.isna(row['institution']) or not row['institution']:
                        errors.append("出版社角色必须指定所在出版社")
                    elif str(row['institution']) not in publishers:
                        errors.append(f"找不到名为 '{row['institution']}' 的出版社")
                    else:
                        publisher_company_id = publishers[str(row['institution'])]
                
                elif row['role'] == 'dealer':
                    # 检查经销商
                    if pd.isna(row['institution']) or not row['institution']:
                        errors.append("经销商角色必须指定所在经销商")
                    elif str(row['institution']) not in dealers:
                        errors.append(f"找不到名为 '{row['institution']}' 的经销商")
                    else:
                        dealer_company_id = dealers[str(row['institution'])]
                
                # 如果有错误，记录并跳过
                if errors:
                    error_records.append({
                        "row": record_num,
                        "username": row['username'] if not pd.isna(row['username']) else "",
                        "errors": errors
                    })
                    continue
                
                # 数据验证通过，准备插入
                name = str(row['name']) if not pd.isna(row['name']) and row['name'] else None
                
                # 插入用户
                cursor.execute(
                    """
                    INSERT INTO users (username, password, name, role, teacher_school_id, publisher_company_id, dealer_company_id, phone_number, email, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    """,
                    (
                        row['username'],
                        generate_password_hash(str(row['password'])),
                        name,
                        row['role'],
                        teacher_school_id,
                        publisher_company_id,
                        dealer_company_id,
                        phone_number,
                        email
                    )
                )

                # 获取新创建的用户ID
                user_id = cursor.lastrowid

                # 如果是出版社用户，创建默认目录
                if row['role'] == 'publisher':
                    try:
                        cursor.execute("""
                            INSERT INTO directories (name, parent_id, publisher_id)
                            VALUES (%s, %s, %s)
                        """, ('默认目录', None, user_id))
                        print(f"为批量导入的出版社用户 {user_id} 创建默认目录成功")
                    except Exception as e:
                        print(f"为批量导入的出版社用户 {user_id} 创建默认目录失败: {str(e)}")
                        # 目录创建失败不影响用户创建成功

                # 将新用户名添加到现有用户名集合中，避免重复导入
                existing_usernames.add(row['username'])
                if phone_number:
                    existing_phones.add(phone_number)
                if email:
                    existing_emails.add(email)

                success_count += 1
            
            # 提交事务
            conn.commit()
            # print(f"导入完成: 成功 {success_count} 条，失败 {len(error_records)} 条")
            
            # 删除上传的文件
            try:
                if os.path.exists(upload_path):
                    os.remove(upload_path)
                    # print(f"已删除上传文件: {upload_path}")
            except Exception as e:
                # print(f"删除上传文件失败: {str(e)}")
                pass
            
            return jsonify({
                "code": 0,
                "message": "导入完成",
                "data": {
                    "total": len(df),
                    "success": success_count,
                    "failure": len(error_records),
                    "success_count": success_count,
                    "error_count": len(error_records),
                    "errors": error_records
                }
            })
            
        except Exception as process_err:
            # 回滚事务
            conn.rollback()
            import traceback
            traceback_info = traceback.format_exc()
            # print(f"导入过程中发生错误: {str(process_err)}")
            # print(f"错误详情: {traceback_info}")
            return jsonify({"code": 1, "message": f"导入过程中发生错误: {str(process_err)}"})
            
    except Exception as e:
        import traceback
        traceback_info = traceback.format_exc()
        # print(f"处理Excel文件失败: {str(e)}")
        # print(f"错误详情: {traceback_info}")
        return jsonify({"code": 1, "message": f"处理Excel文件失败: {str(e)}"})
        
    finally:
        # 关闭数据库连接
        if 'conn' in locals() and conn:
            conn.close()
            # print("数据库连接已关闭")

@admin_bp.route('/get_all_dealers', methods=['GET'])
def get_all_dealers():
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取分页和搜索参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        keyword = request.args.get('keyword', '')
        sort_field = request.args.get('sort_field', 'user_count')  # 默认按用户数量排序
        sort_order = request.args.get('sort_order', 'desc')  # 默认降序
        
        # 检查排序顺序是否有效
        if sort_order not in ['asc', 'desc']:
            sort_order = 'desc'
        
        # 计算分页偏移
        offset = (page - 1) * page_size
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建基础查询
        base_query = """
            SELECT d.id, d.name, d.short_name,
                   dp.can_recommend_books, dp.can_invite_users,
                   dp.can_initiate_exhibition, dp.can_register_exhibition,
                   (SELECT COUNT(*) FROM users WHERE dealer_company_id = d.id) as user_count
            FROM dealer_companies d
            LEFT JOIN dealer_company_permissions dp ON d.id = dp.company_id
        """

        count_query = "SELECT COUNT(*) as total FROM dealer_companies"

        # 添加搜索条件
        if keyword:
            base_query += " WHERE d.name LIKE %s"
            count_query += " WHERE name LIKE %s"
            search_param = f"%{keyword}%"
            count_params = (search_param,)
        else:
            count_params = ()
        
        # 执行查询获取总记录数
        if keyword:
            cursor.execute(count_query, count_params)
        else:
            cursor.execute(count_query)
        
        total = cursor.fetchone()['total']
        
        # 添加排序和分页
        if sort_field == 'user_count':
            base_query += f" ORDER BY user_count {sort_order}, d.name ASC"
        elif sort_field == 'name':
            base_query += f" ORDER BY d.name {sort_order}"
        elif sort_field == 'id':
            base_query += f" ORDER BY d.id {sort_order}"
        else:
            # 默认排序
            base_query += f" ORDER BY user_count DESC, d.name ASC"
            
        base_query += " LIMIT %s OFFSET %s"
        
        # 执行详细查询
        if keyword:
            search_param = f"%{keyword}%"
            cursor.execute(base_query, (search_param, page_size, offset))
        else:
            cursor.execute(base_query, (page_size, offset))
        
        dealers = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size if total > 0 else 1
        
        result = {
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "list": dealers
        }
        
        return jsonify({"code": 0, "message": "获取成功", "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取经销商列表失败: {str(e)}"})

@admin_bp.route('/get_all_schools', methods=['GET'])
def get_all_schools():
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取分页和搜索参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        keyword = request.args.get('keyword', '')
        sort_field = request.args.get('sort_field', 'user_count')  # 默认按用户数量排序
        sort_order = request.args.get('sort_order', 'desc')  # 默认降序
        
        # 检查排序顺序是否有效
        if sort_order not in ['asc', 'desc']:
            sort_order = 'desc'
        
        # 计算分页偏移
        offset = (page - 1) * page_size
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建基础查询
        base_query = """
            SELECT s.id, s.name, s.short_name, s.city, s.school_level,
                   (SELECT COUNT(*) FROM users WHERE teacher_school_id = s.id) as user_count
            FROM schools s
        """
        
        count_query = "SELECT COUNT(*) as total FROM schools"
        
        # 添加搜索条件
        if keyword:
            base_query += " WHERE s.name LIKE %s"
            count_query += " WHERE name LIKE %s"
            search_param = f"%{keyword}%"
            count_params = (search_param,)
        else:
            count_params = ()
        
        # 执行查询获取总记录数
        if keyword:
            cursor.execute(count_query, count_params)
        else:
            cursor.execute(count_query)
        
        total = cursor.fetchone()['total']
        
        # 添加排序和分页
        if sort_field == 'user_count':
            base_query += f" ORDER BY user_count {sort_order}, s.name ASC"
        elif sort_field == 'name':
            base_query += f" ORDER BY s.name {sort_order}"
        elif sort_field == 'id':
            base_query += f" ORDER BY s.id {sort_order}"
        else:
            # 默认排序
            base_query += f" ORDER BY user_count DESC, s.name ASC"
            
        base_query += " LIMIT %s OFFSET %s"
        
        # 执行详细查询
        if keyword:
            search_param = f"%{keyword}%"
            cursor.execute(base_query, (search_param, page_size, offset))
        else:
            cursor.execute(base_query, (page_size, offset))
        
        schools = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size if total > 0 else 1
        
        result = {
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "list": schools
        }
        
        return jsonify({"code": 0, "message": "获取成功", "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取学校列表失败: {str(e)}"})

@admin_bp.route('/get_all_publishers', methods=['GET'])
def get_all_publishers():
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取分页和搜索参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        keyword = request.args.get('keyword', '')
        supplier_type = request.args.get('supplier_type', '')  # 供应商类型筛选
        sort_field = request.args.get('sort_field', 'user_count')  # 默认按用户数量排序
        sort_order = request.args.get('sort_order', 'desc')  # 默认降序
        
        # 检查排序顺序是否有效
        if sort_order not in ['asc', 'desc']:
            sort_order = 'desc'
        
        # 计算分页偏移
        offset = (page - 1) * page_size
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建基础查询
        base_query = """
            SELECT p.id, p.name, p.short_name, p.is_publisher,
                   pp.can_recommend_books, pp.can_register_exhibition,
                   (SELECT COUNT(*) FROM users WHERE publisher_company_id = p.id) as user_count
            FROM publisher_companies p
            LEFT JOIN publisher_company_permissions pp ON p.id = pp.company_id
        """

        count_query = "SELECT COUNT(*) as total FROM publisher_companies"

        # 构建WHERE条件
        where_conditions = []
        params = []

        if keyword:
            where_conditions.append("p.name LIKE %s")
            params.append(f"%{keyword}%")

        if supplier_type:
            where_conditions.append("p.is_publisher = %s")
            params.append(int(supplier_type))

        # 添加WHERE子句
        if where_conditions:
            where_clause = " WHERE " + " AND ".join(where_conditions)
            base_query += where_clause
            count_query += where_clause.replace("p.", "")
            count_params = tuple(params)
        else:
            count_params = ()
        
        # 执行查询获取总记录数
        if count_params:
            cursor.execute(count_query, count_params)
        else:
            cursor.execute(count_query)

        total = cursor.fetchone()['total']

        # 添加排序和分页
        if sort_field == 'user_count':
            base_query += f" ORDER BY user_count {sort_order}, p.name ASC"
        elif sort_field == 'name':
            base_query += f" ORDER BY p.name {sort_order}"
        elif sort_field == 'id':
            base_query += f" ORDER BY p.id {sort_order}"
        else:
            # 默认排序
            base_query += f" ORDER BY user_count DESC, p.name ASC"

        base_query += " LIMIT %s OFFSET %s"

        # 执行详细查询
        query_params = params + [page_size, offset]
        cursor.execute(base_query, query_params)
        
        publishers = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size if total > 0 else 1
        
        result = {
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "list": publishers
        }
        
        return jsonify({"code": 0, "message": "获取成功", "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})

@admin_bp.route('/get_company_users', methods=['GET'])
def get_company_users():
    """获取公司用户列表（学校、出版社或经销商）"""
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        company_id = request.args.get('company_id')
        company_type = request.args.get('company_type')
        
        if not company_id or not company_type:
            return jsonify({"code": 1, "message": "参数不完整"})
        
        company_id = int(company_id)
        
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        if company_type == 'school':
            query = """
                SELECT u.user_id as id, u.username, u.name, u.phone_number as phone,
                       u.email, u.created_at as create_time,
                       CASE
                           WHEN u.role = 'teacher' THEN 'school_teacher'
                           ELSE u.role
                       END as role
                FROM users u
                WHERE u.teacher_school_id = %s
                ORDER BY u.created_at DESC
            """
            cursor.execute(query, (company_id,))
        elif company_type == 'publisher':
            query = """
                SELECT u.user_id as id, u.username, u.name, u.phone_number as phone,
                       u.email, u.created_at as create_time,
                       CASE
                           WHEN u.role = 'publisher' THEN 'publisher_editor'
                           ELSE u.role
                       END as role
                FROM users u
                WHERE u.publisher_company_id = %s
                ORDER BY u.created_at DESC
            """
            cursor.execute(query, (company_id,))
        elif company_type == 'dealer':
            query = """
                SELECT u.user_id as id, u.username, u.name, u.phone_number as phone,
                       u.email, u.created_at as create_time,
                       CASE
                           WHEN u.role = 'dealer' THEN 'dealer_staff'
                           ELSE u.role
                       END as role
                FROM users u
                WHERE u.dealer_company_id = %s
                ORDER BY u.created_at DESC
            """
            cursor.execute(query, (company_id,))
        else:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "无效的公司类型"})
        
        users = cursor.fetchall()
        
        # 格式化时间
        for user in users:
            if user['create_time']:
                user['create_time'] = user['create_time'].strftime('%Y-%m-%d %H:%M:%S')
            # 添加默认激活状态
            user['is_active'] = True
        
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "获取成功", "data": users})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取公司用户列表失败: {str(e)}"})

@admin_bp.route('/update_dealer_permissions', methods=['POST'])
def update_dealer_permissions():
    """
    更新经销商权限设置
    请求参数:
        dealer_id: 经销商ID
        can_recommend_books: 是否有换版推荐权限 (0/1)
        can_invite_users: 是否有邀请用户权限 (0/1)
        can_initiate_exhibition: 是否有发起书展权限 (0/1)
        can_register_exhibition: 是否有书展报名权限 (0/1)
    返回:
        更新结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取请求参数
        dealer_id = request.form.get('dealer_id')
        can_recommend_books = request.form.get('can_recommend_books') in ['true', '1', 'True', 'on', True]
        can_invite_users = request.form.get('can_invite_users') in ['true', '1', 'True', 'on', True]
        can_initiate_exhibition = request.form.get('can_initiate_exhibition') in ['true', '1', 'True', 'on', True]
        can_register_exhibition = request.form.get('can_register_exhibition') in ['true', '1', 'True', 'on', True]
        
        if not dealer_id:
            return jsonify({"code": 1, "message": "参数不完整，缺少经销商ID"})
        
        dealer_id = int(dealer_id)
        
        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查经销商是否存在
        cursor.execute("SELECT id FROM dealer_companies WHERE id = %s", (dealer_id,))
        dealer = cursor.fetchone()
        
        if not dealer:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "经销商不存在"})
        
        # 查询是否已有权限记录
        cursor.execute("SELECT * FROM dealer_company_permissions WHERE company_id = %s", (dealer_id,))
        permission = cursor.fetchone()
        
        # 如果已有记录则更新，否则插入新记录
        if permission:
            cursor.execute("""
                UPDATE dealer_company_permissions 
                SET can_recommend_books = %s, 
                    can_invite_users = %s, 
                    can_initiate_exhibition = %s, 
                    can_register_exhibition = %s,
                    updated_at = NOW()
                WHERE company_id = %s
            """, (
                can_recommend_books, 
                can_invite_users, 
                can_initiate_exhibition, 
                can_register_exhibition, 
                dealer_id
            ))
        else:
            cursor.execute("""
                INSERT INTO dealer_company_permissions 
                (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
            """, (
                dealer_id, 
                can_recommend_books, 
                can_invite_users, 
                can_initiate_exhibition, 
                can_register_exhibition
            ))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "权限设置已更新"})
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"更新经销商权限失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"更新经销商权限失败: {str(e)}"})

@admin_bp.route('/update_publisher_permissions', methods=['POST'])
def update_publisher_permissions():
    """
    更新出版社权限设置
    请求参数:
        publisher_id: 出版社ID
        can_recommend_books: 是否有换版推荐权限 (0/1)
        can_register_exhibition: 是否有书展报名权限 (0/1)
    返回:
        更新结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取请求参数
        publisher_id = request.form.get('publisher_id')
        can_recommend_books = request.form.get('can_recommend_books') in ['true', '1', 'True', 'on', True]
        can_register_exhibition = request.form.get('can_register_exhibition') in ['true', '1', 'True', 'on', True]
        
        if not publisher_id:
            return jsonify({"code": 1, "message": "参数不完整，缺少出版社ID"})
        
        publisher_id = int(publisher_id)
        
        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查出版社是否存在
        cursor.execute("SELECT id FROM publisher_companies WHERE id = %s", (publisher_id,))
        publisher = cursor.fetchone()
        
        if not publisher:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "出版社不存在"})
        
        # 查询是否已有权限记录
        cursor.execute("SELECT * FROM publisher_company_permissions WHERE company_id = %s", (publisher_id,))
        permission = cursor.fetchone()
        
        # 如果已有记录则更新，否则插入新记录
        if permission:
            cursor.execute("""
                UPDATE publisher_company_permissions 
                SET can_recommend_books = %s, 
                    can_register_exhibition = %s,
                    updated_at = NOW()
                WHERE company_id = %s
            """, (
                can_recommend_books, 
                can_register_exhibition, 
                publisher_id
            ))
        else:
            cursor.execute("""
                INSERT INTO publisher_company_permissions 
                (company_id, can_recommend_books, can_register_exhibition, created_at, updated_at)
                VALUES (%s, %s, %s, NOW(), NOW())
            """, (
                publisher_id, 
                can_recommend_books, 
                can_register_exhibition
            ))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return jsonify({"code": 0, "message": "权限设置已更新"})
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"更新出版社权限失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"更新出版社权限失败: {str(e)}"})

@admin_bp.route('/add_school', methods=['POST'])
def add_school():
    """
    添加新学校
    请求参数:
        name: 学校名称
    返回:
        添加结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取请求参数
        school_name = request.form.get('name')
        school_short_name = request.form.get('short_name')
        school_city = request.form.get('city')
        school_level = request.form.get('school_level')

        if not school_name:
            return jsonify({"code": 1, "message": "参数不完整，缺少学校名称"})

        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查学校名称是否已存在
        cursor.execute("SELECT id FROM schools WHERE name = %s", (school_name,))
        existing_school = cursor.fetchone()

        if existing_school:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "学校名称已存在"})

        # 插入新学校记录
        cursor.execute("INSERT INTO schools (name, short_name, city, school_level) VALUES (%s, %s, %s, %s)",
                      (school_name, school_short_name, school_city, school_level))
        connection.commit()

        # 获取新插入记录的ID
        new_school_id = cursor.lastrowid

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "学校添加成功",
            "data": {
                "id": new_school_id,
                "name": school_name,
                "short_name": school_short_name,
                "city": school_city,
                "school_level": school_level
            }
        })
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"添加学校失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"添加学校失败: {str(e)}"})

@admin_bp.route('/update_school', methods=['POST'])
def update_school():
    """
    更新学校信息
    请求参数:
        school_id: 学校ID
        name: 学校名称
        city: 所在城市
        school_level: 办学层次
    返回:
        更新结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403

    try:
        # 获取请求参数
        school_id = request.form.get('school_id')
        school_name = request.form.get('name')
        school_short_name = request.form.get('short_name')
        school_city = request.form.get('city')
        school_level = request.form.get('school_level')

        if not school_id or not school_name:
            return jsonify({"code": 1, "message": "参数不完整，缺少学校ID或名称"})

        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查学校是否存在
        cursor.execute("SELECT id FROM schools WHERE id = %s", (school_id,))
        existing_school = cursor.fetchone()

        if not existing_school:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "学校不存在"})

        # 检查学校名称是否与其他学校重复
        cursor.execute("SELECT id FROM schools WHERE name = %s AND id != %s", (school_name, school_id))
        duplicate_school = cursor.fetchone()

        if duplicate_school:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "学校名称已存在"})

        # 更新学校记录
        cursor.execute("UPDATE schools SET name = %s, short_name = %s, city = %s, school_level = %s WHERE id = %s",
                      (school_name, school_short_name, school_city, school_level, school_id))
        connection.commit()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "学校信息更新成功",
            "data": {
                "id": int(school_id),
                "name": school_name,
                "short_name": school_short_name,
                "city": school_city,
                "school_level": school_level
            }
        })
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"更新学校失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"更新学校失败: {str(e)}"})

@admin_bp.route('/add_publisher', methods=['POST'])
def add_publisher():
    """
    添加新出版社
    请求参数:
        name: 出版社名称
        address: 地址
        contact_phone: 联系电话
        is_publisher: 是否仅为出版社 (0/1)
        can_recommend_books: 是否有换版推荐权限 (0/1)
        can_register_exhibition: 是否有书展报名权限 (0/1)
    返回:
        添加结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取请求参数
        name = request.form.get('name')
        short_name = request.form.get('short_name', '')
        address = request.form.get('address', '')
        contact_phone = request.form.get('contact_phone', '')
        is_publisher = request.form.get('is_publisher') in ['true', '1', 'True', 'on', True]
        can_recommend_books = request.form.get('can_recommend_books') in ['true', '1', 'True', 'on', True]
        can_register_exhibition = request.form.get('can_register_exhibition') in ['true', '1', 'True', 'on', True]

        if not name:
            return jsonify({"code": 1, "message": "参数不完整，缺少出版社名称"})

        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查出版社名称是否已存在
        cursor.execute("SELECT id FROM publisher_companies WHERE name = %s", (name,))
        existing_publisher = cursor.fetchone()

        if existing_publisher:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "出版社名称已存在"})

        # 插入新出版社记录
        cursor.execute("""
            INSERT INTO publisher_companies
            (name, short_name, address, contact_phone, is_publisher, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
        """, (name, short_name, address, contact_phone, is_publisher))
        connection.commit()
        
        # 获取新插入记录的ID
        new_publisher_id = cursor.lastrowid
        
        # 插入权限记录
        cursor.execute("""
            INSERT INTO publisher_company_permissions 
            (company_id, can_recommend_books, can_register_exhibition, created_at, updated_at)
            VALUES (%s, %s, %s, NOW(), NOW())
        """, (new_publisher_id, can_recommend_books, can_register_exhibition))
        connection.commit()
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "出版社添加成功",
            "data": {
                "id": new_publisher_id,
                "name": name,
                "short_name": short_name
            }
        })
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"添加出版社失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"添加出版社失败: {str(e)}"})

@admin_bp.route('/get_publisher_details', methods=['GET'])
def get_publisher_details():
    """获取供应商详情"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        publisher_id = request.args.get('id')
        if not publisher_id:
            return jsonify({"code": 1, "message": "缺少供应商ID"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询供应商基本信息
        cursor.execute("""
            SELECT p.id, p.name, p.address, p.contact_phone, p.is_publisher,
                   pp.can_recommend_books, pp.can_register_exhibition
            FROM publisher_companies p
            LEFT JOIN publisher_company_permissions pp ON p.id = pp.company_id
            WHERE p.id = %s
        """, (publisher_id,))

        publisher = cursor.fetchone()

        if not publisher:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "供应商不存在"})

        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "获取成功", "data": publisher})

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取供应商详情失败: {str(e)}"})

@admin_bp.route('/update_publisher', methods=['POST'])
def update_publisher():
    """更新供应商信息"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 获取表单数据
        publisher_id = request.form.get('id')
        name = request.form.get('name', '').strip()
        short_name = request.form.get('short_name', '').strip()
        address = request.form.get('address', '').strip()
        contact_phone = request.form.get('contact_phone', '').strip()
        is_publisher = request.form.get('is_publisher') == 'true'
        can_recommend_books = request.form.get('can_recommend_books') == 'true'
        can_register_exhibition = request.form.get('can_register_exhibition') == 'true'

        # 验证必填字段
        if not publisher_id or not name:
            return jsonify({"code": 1, "message": "供应商ID和名称不能为空"})

        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查供应商是否存在
        cursor.execute("SELECT id FROM publisher_companies WHERE id = %s", (publisher_id,))
        if not cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "供应商不存在"})

        # 检查名称是否与其他供应商重复
        cursor.execute("SELECT id FROM publisher_companies WHERE name = %s AND id != %s", (name, publisher_id))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "供应商名称已存在"})

        # 更新供应商基本信息
        cursor.execute("""
            UPDATE publisher_companies
            SET name = %s, short_name = %s, address = %s, contact_phone = %s, is_publisher = %s
            WHERE id = %s
        """, (name, short_name, address, contact_phone, is_publisher, publisher_id))

        # 更新或插入权限信息
        cursor.execute("SELECT id FROM publisher_company_permissions WHERE company_id = %s", (publisher_id,))
        if cursor.fetchone():
            # 更新现有权限
            cursor.execute("""
                UPDATE publisher_company_permissions
                SET can_recommend_books = %s, can_register_exhibition = %s, updated_at = NOW()
                WHERE company_id = %s
            """, (can_recommend_books, can_register_exhibition, publisher_id))
        else:
            # 插入新权限记录
            cursor.execute("""
                INSERT INTO publisher_company_permissions
                (company_id, can_recommend_books, can_register_exhibition, created_at, updated_at)
                VALUES (%s, %s, %s, NOW(), NOW())
            """, (publisher_id, can_recommend_books, can_register_exhibition))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "供应商更新成功"})

    except Exception as e:
        return jsonify({"code": 1, "message": f"更新供应商失败: {str(e)}"})

@admin_bp.route('/add_dealer', methods=['POST'])
def add_dealer():
    """
    添加新经销商
    请求参数:
        name: 经销商名称
        address: 地址
        contact_phone: 联系电话
        parent_company_id: 上级公司ID（可选）
        can_recommend_books: 是否有换版推荐权限 (0/1)
        can_invite_users: 是否有邀请用户权限 (0/1)
        can_initiate_exhibition: 是否有发起书展权限 (0/1)
        can_register_exhibition: 是否有书展报名权限 (0/1)
    返回:
        添加结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取请求参数
        name = request.form.get('name')
        short_name = request.form.get('short_name', '')
        address = request.form.get('address', '')
        contact_phone = request.form.get('contact_phone', '')
        parent_company_id = request.form.get('parent_company_id')
        
        can_recommend_books = request.form.get('can_recommend_books') in ['true', '1', 'True', 'on', True]
        can_invite_users = request.form.get('can_invite_users') in ['true', '1', 'True', 'on', True]
        can_initiate_exhibition = request.form.get('can_initiate_exhibition') in ['true', '1', 'True', 'on', True]
        can_register_exhibition = request.form.get('can_register_exhibition') in ['true', '1', 'True', 'on', True]
        
        if not name:
            return jsonify({"code": 1, "message": "参数不完整，缺少经销商名称"})
        
        # 处理parent_company_id（如果提供）
        if parent_company_id:
            try:
                parent_company_id = int(parent_company_id)
            except (ValueError, TypeError):
                return jsonify({"code": 1, "message": "上级公司ID格式错误"})
        else:
            parent_company_id = None
        
        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查经销商名称是否已存在
        cursor.execute("SELECT id FROM dealer_companies WHERE name = %s", (name,))
        existing_dealer = cursor.fetchone()
        
        if existing_dealer:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "经销商名称已存在"})
        
        # 如果提供了上级公司ID，检查其是否存在
        if parent_company_id:
            cursor.execute("SELECT id FROM dealer_companies WHERE id = %s", (parent_company_id,))
            parent_company = cursor.fetchone()
            
            if not parent_company:
                cursor.close()
                connection.close()
                return jsonify({"code": 1, "message": "上级公司不存在"})
        
        # 插入新经销商记录
        cursor.execute("""
            INSERT INTO dealer_companies
            (name, short_name, address, contact_phone, parent_company_id, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
        """, (name, short_name, address, contact_phone, parent_company_id))
        connection.commit()
        
        # 获取新插入记录的ID
        new_dealer_id = cursor.lastrowid
        
        # 插入权限记录
        cursor.execute("""
            INSERT INTO dealer_company_permissions 
            (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_dealer_id, 
            can_recommend_books, 
            can_invite_users, 
            can_initiate_exhibition, 
            can_register_exhibition
        ))
        connection.commit()
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "经销商添加成功",
            "data": {
                "id": new_dealer_id,
                "name": name,
                "short_name": short_name
            }
        })
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"添加经销商失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"添加经销商失败: {str(e)}"})

@admin_bp.route('/update_dealer', methods=['POST'])
def update_dealer():
    """
    更新经销商信息
    请求参数:
        id: 经销商ID
        name: 经销商名称
        short_name: 经销商简称
        address: 地址
        contact_phone: 联系电话
        can_recommend_books: 是否有换版推荐权限 (0/1)
        can_invite_users: 是否有邀请用户权限 (0/1)
        can_initiate_exhibition: 是否有发起书展权限 (0/1)
        can_register_exhibition: 是否有书展报名权限 (0/1)
    返回:
        更新结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403

    try:
        # 获取请求参数
        dealer_id = request.form.get('id')
        name = request.form.get('name', '').strip()
        short_name = request.form.get('short_name', '').strip()
        address = request.form.get('address', '').strip()
        contact_phone = request.form.get('contact_phone', '').strip()

        # 获取权限参数
        can_recommend_books = request.form.get('can_recommend_books') in ['true', '1', 'True', 'on', True]
        can_invite_users = request.form.get('can_invite_users') in ['true', '1', 'True', 'on', True]
        can_initiate_exhibition = request.form.get('can_initiate_exhibition') in ['true', '1', 'True', 'on', True]
        can_register_exhibition = request.form.get('can_register_exhibition') in ['true', '1', 'True', 'on', True]

        # 验证必填字段
        if not dealer_id or not name:
            return jsonify({"code": 1, "message": "经销商ID和名称不能为空"})

        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查经销商是否存在
        cursor.execute("SELECT id FROM dealer_companies WHERE id = %s", (dealer_id,))
        if not cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "经销商不存在"})

        # 检查名称是否与其他经销商重复
        cursor.execute("SELECT id FROM dealer_companies WHERE name = %s AND id != %s", (name, dealer_id))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "经销商名称已存在"})

        # 更新经销商基本信息
        cursor.execute("""
            UPDATE dealer_companies
            SET name = %s, short_name = %s, address = %s, contact_phone = %s
            WHERE id = %s
        """, (name, short_name, address, contact_phone, dealer_id))

        # 更新或插入权限信息
        cursor.execute("SELECT id FROM dealer_company_permissions WHERE company_id = %s", (dealer_id,))
        if cursor.fetchone():
            # 更新现有权限
            cursor.execute("""
                UPDATE dealer_company_permissions
                SET can_recommend_books = %s, can_invite_users = %s,
                    can_initiate_exhibition = %s, can_register_exhibition = %s, updated_at = NOW()
                WHERE company_id = %s
            """, (can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition, dealer_id))
        else:
            # 插入新权限记录
            cursor.execute("""
                INSERT INTO dealer_company_permissions
                (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
            """, (dealer_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "经销商更新成功"})

    except Exception as e:
        return jsonify({"code": 1, "message": f"更新经销商失败: {str(e)}"})

@admin_bp.route('/delete_school', methods=['POST'])
def delete_school():
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取学校ID
        school_id = request.json.get('id')
        if not school_id:
            return jsonify({"code": 1, "message": "学校ID不能为空"}), 400
        
        # 检查学校是否存在关联用户
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查询是否有教师关联到该学校
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE teacher_school_id = %s", (school_id,))
        result = cursor.fetchone()
        if result and result['count'] > 0:
            return jsonify({"code": 1, "message": f"该学校下有{result['count']}个关联用户，无法删除"}), 400
        
        # 查询学校是否与书展活动相关联
        cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE school_id = %s", (school_id,))
        result = cursor.fetchone()
        if result and result['count'] > 0:
            return jsonify({"code": 1, "message": f"该学校与{result['count']}个书展活动相关联，无法删除"}), 400
        
        # 执行删除操作
        cursor.execute("DELETE FROM schools WHERE id = %s", (school_id,))
        conn.commit()
        
        return jsonify({"code": 0, "message": "学校删除成功"})
        
    except Exception as e:
        # print(f"删除学校时出错: {str(e)}")
        return jsonify({"code": 1, "message": f"删除学校时出错: {str(e)}"}), 500
    
    finally:
        if 'conn' in locals() and conn:
            conn.close()

@admin_bp.route('/delete_publisher', methods=['POST'])
def delete_publisher():
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取出版社ID
        publisher_id = request.json.get('id')
        if not publisher_id:
            return jsonify({"code": 1, "message": "出版社ID不能为空"}), 400
        
        # 检查出版社是否存在关联用户
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查询是否有用户关联到该出版社
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE publisher_company_id = %s", (publisher_id,))
        result = cursor.fetchone()
        if result and result['count'] > 0:
            return jsonify({"code": 1, "message": f"该出版社下有{result['count']}个关联用户，无法删除"}), 400
        
        # 执行删除出版社权限表记录
        cursor.execute("DELETE FROM publisher_company_permissions WHERE company_id = %s", (publisher_id,))
        
        # 执行删除出版社操作
        cursor.execute("DELETE FROM publisher_companies WHERE id = %s", (publisher_id,))
        conn.commit()
        
        return jsonify({"code": 0, "message": "出版社删除成功"})
        
    except Exception as e:
        # print(f"删除出版社时出错: {str(e)}")
        return jsonify({"code": 1, "message": f"删除出版社时出错: {str(e)}"}), 500
    
    finally:
        if 'conn' in locals() and conn:
            conn.close()

@admin_bp.route('/delete_dealer', methods=['POST'])
def delete_dealer():
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取经销商ID
        dealer_id = request.json.get('id')
        if not dealer_id:
            return jsonify({"code": 1, "message": "经销商ID不能为空"}), 400
        
        # 检查经销商是否存在关联用户
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查询是否有用户关联到该经销商
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE dealer_company_id = %s", (dealer_id,))
        result = cursor.fetchone()
        if result and result['count'] > 0:
            return jsonify({"code": 1, "message": f"该经销商下有{result['count']}个关联用户，无法删除"}), 400
        
        # 执行删除经销商权限表记录
        cursor.execute("DELETE FROM dealer_company_permissions WHERE company_id = %s", (dealer_id,))
        
        # 执行删除经销商操作
        cursor.execute("DELETE FROM dealer_companies WHERE id = %s", (dealer_id,))
        conn.commit()
        
        return jsonify({"code": 0, "message": "经销商删除成功"})
        
    except Exception as e:
        # print(f"删除经销商时出错: {str(e)}")
        return jsonify({"code": 1, "message": f"删除经销商时出错: {str(e)}"}), 500
    
    finally:
        if 'conn' in locals() and conn:
            conn.close()

@admin_bp.route('/batch_update_publisher_permissions', methods=['POST'])
def batch_update_publisher_permissions():
    """
    批量更新出版社权限设置
    请求参数:
        publisher_ids: 出版社ID列表
        permission_type: 权限类型 (can_recommend_books, can_register_exhibition)
        permission_value: 权限值 (true/false)
    返回:
        更新结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取请求参数
        data = request.get_json()
        publisher_ids = data.get('publisher_ids', [])
        permission_type = data.get('permission_type')
        permission_value = data.get('permission_value') in [True, 'true', 1, '1']
        
        if not publisher_ids or not permission_type:
            return jsonify({"code": 1, "message": "参数不完整，缺少出版社ID列表或权限类型"})
        
        # 验证权限类型是否有效
        valid_permissions = ['can_recommend_books', 'can_register_exhibition']
        if permission_type not in valid_permissions:
            return jsonify({"code": 1, "message": f"无效的权限类型，有效值为: {', '.join(valid_permissions)}"})
        
        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 处理每个出版社
        updated_count = 0
        for publisher_id in publisher_ids:
            # 检查出版社是否存在
            cursor.execute("SELECT id FROM publisher_companies WHERE id = %s", (publisher_id,))
            publisher = cursor.fetchone()
            
            if not publisher:
                continue
            
            # 查询是否已有权限记录
            cursor.execute("SELECT * FROM publisher_company_permissions WHERE company_id = %s", (publisher_id,))
            permission = cursor.fetchone()
            
            # 如果已有记录则更新，否则插入新记录
            if permission:
                query = f"""
                    UPDATE publisher_company_permissions 
                    SET {permission_type} = %s,
                        updated_at = NOW()
                    WHERE company_id = %s
                """
                cursor.execute(query, (permission_value, publisher_id))
            else:
                # 为新记录设置默认值
                default_values = {
                    'can_recommend_books': False,
                    'can_register_exhibition': False
                }
                default_values[permission_type] = permission_value
                
                query = """
                    INSERT INTO publisher_company_permissions 
                    (company_id, can_recommend_books, can_register_exhibition, created_at, updated_at)
                    VALUES (%s, %s, %s, NOW(), NOW())
                """
                cursor.execute(query, (
                    publisher_id, 
                    default_values['can_recommend_books'], 
                    default_values['can_register_exhibition']
                ))
            
            updated_count += 1
        
        connection.commit()
        cursor.close()
        connection.close()
        
        if updated_count > 0:
            return jsonify({"code": 0, "message": f"已成功更新 {updated_count} 家出版社的权限设置"})
        else:
            return jsonify({"code": 1, "message": "未找到有效的出版社，未进行任何更新"})
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"批量更新出版社权限失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"批量更新出版社权限失败: {str(e)}"})

@admin_bp.route('/batch_update_dealer_permissions', methods=['POST'])
def batch_update_dealer_permissions():
    """
    批量更新经销商权限设置
    请求参数:
        dealer_ids: 经销商ID列表
        permission_type: 权限类型 (can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition)
        permission_value: 权限值 (true/false)
    返回:
        更新结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取请求参数
        data = request.get_json()
        dealer_ids = data.get('dealer_ids', [])
        permission_type = data.get('permission_type')
        permission_value = data.get('permission_value') in [True, 'true', 1, '1']
        
        if not dealer_ids or not permission_type:
            return jsonify({"code": 1, "message": "参数不完整，缺少经销商ID列表或权限类型"})
        
        # 验证权限类型是否有效
        valid_permissions = ['can_recommend_books', 'can_invite_users', 'can_initiate_exhibition', 'can_register_exhibition']
        if permission_type not in valid_permissions:
            return jsonify({"code": 1, "message": f"无效的权限类型，有效值为: {', '.join(valid_permissions)}"})
        
        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 处理每个经销商
        updated_count = 0
        for dealer_id in dealer_ids:
            # 检查经销商是否存在
            cursor.execute("SELECT id FROM dealer_companies WHERE id = %s", (dealer_id,))
            dealer = cursor.fetchone()
            
            if not dealer:
                continue
            
            # 查询是否已有权限记录
            cursor.execute("SELECT * FROM dealer_company_permissions WHERE company_id = %s", (dealer_id,))
            permission = cursor.fetchone()
            
            # 如果已有记录则更新，否则插入新记录
            if permission:
                query = f"""
                    UPDATE dealer_company_permissions 
                    SET {permission_type} = %s,
                        updated_at = NOW()
                    WHERE company_id = %s
                """
                cursor.execute(query, (permission_value, dealer_id))
            else:
                # 为新记录设置默认值
                default_values = {
                    'can_recommend_books': False,
                    'can_invite_users': False,
                    'can_initiate_exhibition': False,
                    'can_register_exhibition': False
                }
                default_values[permission_type] = permission_value
                
                query = """
                    INSERT INTO dealer_company_permissions 
                    (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                """
                cursor.execute(query, (
                    dealer_id, 
                    default_values['can_recommend_books'], 
                    default_values['can_invite_users'], 
                    default_values['can_initiate_exhibition'], 
                    default_values['can_register_exhibition']
                ))
            
            updated_count += 1
        
        connection.commit()
        cursor.close()
        connection.close()
        
        if updated_count > 0:
            return jsonify({"code": 0, "message": f"已成功更新 {updated_count} 家经销商的权限设置"})
        else:
            return jsonify({"code": 1, "message": "未找到有效的经销商，未进行任何更新"})
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"批量更新经销商权限失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"批量更新经销商权限失败: {str(e)}"})

@admin_bp.route('/download_organization_import_template', methods=['GET'])
def download_organization_import_template():
    """
    下载组织导入模板
    请求参数:
        type: 组织类型 (school, publisher, dealer)
    返回:
        Excel模板文件
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 获取组织类型
        org_type = request.args.get('type', 'school')
        if org_type not in ['school', 'publisher', 'dealer']:
            return jsonify({"code": 1, "message": "无效的组织类型"}), 400
        
        # 获取应用根目录
        from flask import current_app
        app_root = current_app.root_path
        
        # 确保static/excel目录存在
        excel_dir = os.path.join(app_root, 'static', 'excel')
        os.makedirs(excel_dir, exist_ok=True)
        
        # 定义模板文件路径
        template_path = os.path.join(excel_dir, f'{org_type}_import_template.xlsx')
        
        # 根据组织类型创建不同的模板
        if org_type == 'school':
            # 创建学校模板
            data = {
                '学校名称*': ['示例学校1', '示例学校2', '示例学校3'],
                '城市': ['北京市', '上海市', '广州市'],
                '办学层次': ['本科', '专科', '中职'],
            }
            df = pd.DataFrame(data)

            # 使用xlsxwriter创建带格式的Excel文件
            import xlsxwriter
            workbook = xlsxwriter.Workbook(template_path)
            worksheet = workbook.add_worksheet('学校导入模板')

            # 定义格式
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#D7E4BC',
                'border': 1,
                'align': 'center',
                'valign': 'vcenter'
            })

            required_format = workbook.add_format({
                'bg_color': '#FFE699',
                'border': 1,
                'align': 'center',
                'valign': 'vcenter'
            })

            optional_format = workbook.add_format({
                'bg_color': '#F2F2F2',
                'border': 1,
                'align': 'center',
                'valign': 'vcenter'
            })

            # 设置列宽
            worksheet.set_column('A:A', 20)  # 学校名称
            worksheet.set_column('B:B', 15)  # 城市
            worksheet.set_column('C:C', 15)  # 办学层次

            # 写入表头
            headers = ['学校名称*', '城市', '办学层次']
            for col, header in enumerate(headers):
                if '*' in header:
                    worksheet.write(0, col, header, required_format)
                else:
                    worksheet.write(0, col, header, optional_format)

            # 写入示例数据
            for row in range(len(data['学校名称*'])):
                worksheet.write(row + 1, 0, data['学校名称*'][row])
                worksheet.write(row + 1, 1, data['城市'][row])
                worksheet.write(row + 1, 2, data['办学层次'][row])

            # 添加说明页
            instruction_sheet = workbook.add_worksheet('填写说明')
            instruction_sheet.set_column('A:A', 100)

            instructions = [
                "学校导入模板使用说明：",
                "1. 必填字段（黄色背景）：学校名称*",
                "2. 可选字段（灰色背景）：城市、办学层次",
                "3. 学校名称不能重复，系统会自动检查",
                "4. 城市字段建议填写市级名称，如：北京市、上海市、广州市等",
                "5. 办学层次可选值：本科、专科、中职等",
                "6. 请不要修改表头和格式",
            ]

            for i, instruction in enumerate(instructions):
                instruction_sheet.write(i, 0, instruction)

            workbook.close()
        elif org_type == 'publisher':
            # 创建出版社模板
            data = {
                '出版社名称': ['示例出版社1', '示例出版社2', '示例出版社3'],
                '地址': ['示例地址1', '示例地址2', '示例地址3'],
                '联系电话': ['13800000001', '13800000002', '13800000003'],
                '仅为出版社': [1, 1, 0],  # 1表示仅为出版社
                '允许换版推荐': [1, 0, 1],  # 1表示允许换版推荐
                '允许书展报名': [1, 0, 1],  # 1表示允许书展报名
            }
            df = pd.DataFrame(data)
            df.to_excel(template_path, index=False)
        elif org_type == 'dealer':
            # 创建经销商模板
            data = {
                '经销商名称': ['示例经销商1', '示例经销商2', '示例经销商3'],
                '地址': ['示例地址1', '示例地址2', '示例地址3'],
                '联系电话': ['13800000001', '13800000002', '13800000003'],
                '上级经销商名称': ['', '示例经销商1', ''],  # 上级经销商名称，留空表示无上级
                '允许换版推荐': [1, 0, 1],  # 1表示允许换版推荐
                '允许邀请用户': [1, 1, 0],  # 1表示允许邀请用户
                '允许发起书展': [1, 0, 0],  # 1表示允许发起书展
                '允许书展报名': [1, 1, 1],  # 1表示允许书展报名
            }
            df = pd.DataFrame(data)
            df.to_excel(template_path, index=False)
        
        # 返回文件
        return send_file(template_path, as_attachment=True, download_name=f'{org_type}_import_template.xlsx')
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"下载导入模板失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"下载导入模板失败: {str(e)}"}), 500

@admin_bp.route('/import_schools', methods=['POST'])
def import_schools():
    """
    批量导入学校
    请求数据:
        上传的Excel文件
    返回:
        导入结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 检查是否上传了文件
        # print("接收到导入学校请求")
        if 'file' not in request.files:
            # print("未找到上传的文件")
            return jsonify({"code": 1, "message": "未上传文件"}), 400
        
        file = request.files['file']
        if file.filename == '':
            # print("文件名为空")
            return jsonify({"code": 1, "message": "未选择文件"}), 400
        
        # print(f"收到上传文件: {file.filename}")
        
        # 检查文件格式
        if not file.filename.endswith(('.xlsx', '.xls')):
            # print(f"文件格式错误: {file.filename}")
            return jsonify({"code": 1, "message": "文件格式错误，请上传Excel文件"}), 400
        
        # 读取Excel文件
        try:
            df = pd.read_excel(file)
            # print(f"成功读取Excel文件，共 {len(df)} 行数据")
        except Exception as e:
            # print(f"Excel文件读取失败: {str(e)}")
            return jsonify({"code": 1, "message": f"Excel文件读取失败: {str(e)}"}), 400
        
        # 检查必要列是否存在 - 支持中文表头
        required_columns = ['学校名称*']
        optional_columns = ['城市', '办学层次']

        # 兼容旧版本模板（学校名称）
        if '学校名称*' not in df.columns and '学校名称' in df.columns:
            df = df.rename(columns={'学校名称': '学校名称*'})

        column_mapping = {
            '学校名称*': 'name',
            '城市': 'city',
            '办学层次': 'school_level'
        }

        for col in required_columns:
            if col not in df.columns:
                # print(f"缺少必要列: {col}")
                return jsonify({"code": 1, "message": f"缺少必要列: {col}"}), 400

        # 重命名列以符合代码处理
        df = df.rename(columns=column_mapping)
        
        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 导入数据
        success_count = 0
        error_count = 0
        error_details = []
        existing_schools = set()
        
        # 获取已存在的学校名称
        cursor.execute("SELECT name FROM schools")
        for row in cursor.fetchall():
            existing_schools.add(row['name'].lower())
        
        # 处理每一行数据
        for index, row in df.iterrows():
            try:
                school_name = str(row['name']).strip()

                # 跳过空名称
                if not school_name or pd.isna(school_name):
                    error_count += 1
                    error_details.append(f"行 {index+2}: 学校名称不能为空")
                    continue

                # 检查学校名称是否已存在
                if school_name.lower() in existing_schools:
                    error_count += 1
                    error_details.append(f"行 {index+2}: 学校 '{school_name}' 已存在")
                    continue

                # 处理可选字段
                city = None
                school_level = None

                if 'city' in row and not pd.isna(row['city']):
                    city = str(row['city']).strip()
                    if not city:
                        city = None

                if 'school_level' in row and not pd.isna(row['school_level']):
                    school_level = str(row['school_level']).strip()
                    if not school_level:
                        school_level = None

                # 插入新学校
                cursor.execute(
                    "INSERT INTO schools (name, city, school_level) VALUES (%s, %s, %s)",
                    (school_name, city, school_level)
                )
                existing_schools.add(school_name.lower())
                success_count += 1
            except Exception as e:
                error_count += 1
                error_details.append(f"行 {index+2}: {str(e)}")
        
        # 提交事务
        connection.commit()
        cursor.close()
        connection.close()
        
        # print(f"导入完成: 成功 {success_count} 条, 失败 {error_count} 条")
        return jsonify({
            "code": 0,
            "message": f"导入完成: 成功 {success_count} 条, 失败 {error_count} 条",
            "data": {
                "success_count": success_count,
                "fail_count": error_count,
                "error_details": error_details
            }
        })
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"导入学校失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"导入学校失败: {str(e)}"}), 500

@admin_bp.route('/import_publishers', methods=['POST'])
def import_publishers():
    """
    批量导入出版社
    请求数据:
        上传的Excel文件
    返回:
        导入结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 检查是否上传了文件
        # print("接收到导入出版社请求")
        if 'file' not in request.files:
            # print("未找到上传的文件")
            return jsonify({"code": 1, "message": "未上传文件"}), 400
        
        file = request.files['file']
        if file.filename == '':
            # print("文件名为空")
            return jsonify({"code": 1, "message": "未选择文件"}), 400
        
        # print(f"收到上传文件: {file.filename}")
        
        # 检查文件格式
        if not file.filename.endswith(('.xlsx', '.xls')):
            # print(f"文件格式错误: {file.filename}")
            return jsonify({"code": 1, "message": "文件格式错误，请上传Excel文件"}), 400
        
        # 读取Excel文件
        try:
            df = pd.read_excel(file)
            # print(f"成功读取Excel文件，共 {len(df)} 行数据")
        except Exception as e:
            # print(f"Excel文件读取失败: {str(e)}")
            return jsonify({"code": 1, "message": f"Excel文件读取失败: {str(e)}"}), 400
        
        # 检查必要列是否存在 - 支持中文表头
        required_columns = ['出版社名称']
        column_mapping = {
            '出版社名称': 'name',
            '地址': 'address',
            '联系电话': 'contact_phone',
            '仅为出版社': 'is_publisher',
            '允许换版推荐': 'can_recommend_books',
            '允许书展报名': 'can_register_exhibition'
        }
        
        for col in required_columns:
            if col not in df.columns:
                # print(f"缺少必要列: {col}")
                return jsonify({"code": 1, "message": f"缺少必要列: {col}"}), 400
        
        # 重命名列以符合代码处理
        for col in df.columns:
            if col in column_mapping:
                df = df.rename(columns={col: column_mapping[col]})
        
        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 导入数据
        success_count = 0
        error_count = 0
        error_details = []
        existing_publishers = set()
        
        # 获取已存在的出版社名称
        cursor.execute("SELECT name FROM publisher_companies")
        for row in cursor.fetchall():
            existing_publishers.add(row['name'].lower())
        
        # 处理每一行数据
        for index, row in df.iterrows():
            try:
                name = str(row['name']).strip()
                
                # 跳过空名称
                if not name or pd.isna(name):
                    error_count += 1
                    error_details.append(f"行 {index+2}: 出版社名称不能为空")
                    continue
                
                # 检查名称是否已存在
                if name.lower() in existing_publishers:
                    error_count += 1
                    error_details.append(f"行 {index+2}: 出版社 '{name}' 已存在")
                    continue
                
                # 处理可选字段
                address = str(row.get('address', '')).strip() if not pd.isna(row.get('address', '')) else None
                contact_phone = str(row.get('contact_phone', '')).strip() if not pd.isna(row.get('contact_phone', '')) else None
                is_publisher = bool(row.get('is_publisher', True)) if not pd.isna(row.get('is_publisher', True)) else True
                
                # 处理权限字段
                can_recommend_books = bool(row.get('can_recommend_books', False)) if not pd.isna(row.get('can_recommend_books', False)) else False
                can_register_exhibition = bool(row.get('can_register_exhibition', False)) if not pd.isna(row.get('can_register_exhibition', False)) else False
                
                # 插入出版社记录
                cursor.execute("""
                    INSERT INTO publisher_companies 
                    (name, address, contact_phone, is_publisher, created_at) 
                    VALUES (%s, %s, %s, %s, NOW())
                """, (name, address, contact_phone, is_publisher))
                
                publisher_id = cursor.lastrowid
                
                # 插入权限记录
                cursor.execute("""
                    INSERT INTO publisher_company_permissions 
                    (company_id, can_recommend_books, can_register_exhibition, created_at, updated_at)
                    VALUES (%s, %s, %s, NOW(), NOW())
                """, (publisher_id, can_recommend_books, can_register_exhibition))
                
                existing_publishers.add(name.lower())
                success_count += 1
            except Exception as e:
                error_count += 1
                error_details.append(f"行 {index+2}: {str(e)}")
        
        # 提交事务
        connection.commit()
        cursor.close()
        connection.close()
        
        # print(f"导入完成: 成功 {success_count} 条, 失败 {error_count} 条")
        return jsonify({
            "code": 0,
            "message": f"导入完成: 成功 {success_count} 条, 失败 {error_count} 条",
            "data": {
                "success_count": success_count,
                "fail_count": error_count,
                "error_details": error_details
            }
        })
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"导入出版社失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"导入出版社失败: {str(e)}"}), 500

@admin_bp.route('/import_dealers', methods=['POST'])
def import_dealers():
    """
    批量导入经销商
    请求数据:
        上传的Excel文件
    返回:
        导入结果信息
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"}), 403
    
    try:
        # 检查是否上传了文件
        # print("接收到导入经销商请求")
        if 'file' not in request.files:
            # print("未找到上传的文件")
            return jsonify({"code": 1, "message": "未上传文件"}), 400
        
        file = request.files['file']
        if file.filename == '':
            # print("文件名为空")
            return jsonify({"code": 1, "message": "未选择文件"}), 400
        
        # print(f"收到上传文件: {file.filename}")
        
        # 检查文件格式
        if not file.filename.endswith(('.xlsx', '.xls')):
            # print(f"文件格式错误: {file.filename}")
            return jsonify({"code": 1, "message": "文件格式错误，请上传Excel文件"}), 400
        
        # 读取Excel文件
        try:
            df = pd.read_excel(file)
            # print(f"成功读取Excel文件，共 {len(df)} 行数据")
        except Exception as e:
            # print(f"Excel文件读取失败: {str(e)}")
            return jsonify({"code": 1, "message": f"Excel文件读取失败: {str(e)}"}), 400
        
        # 检查必要列是否存在 - 支持中文表头
        required_columns = ['经销商名称']
        column_mapping = {
            '经销商名称': 'name',
            '地址': 'address',
            '联系电话': 'contact_phone',
            '上级经销商名称': 'parent_company_name',
            '允许换版推荐': 'can_recommend_books',
            '允许邀请用户': 'can_invite_users',
            '允许发起书展': 'can_initiate_exhibition',
            '允许书展报名': 'can_register_exhibition'
        }
        
        for col in required_columns:
            if col not in df.columns:
                # print(f"缺少必要列: {col}")
                return jsonify({"code": 1, "message": f"缺少必要列: {col}"}), 400
        
        # 重命名列以符合代码处理
        for col in df.columns:
            if col in column_mapping:
                df = df.rename(columns={col: column_mapping[col]})
        
        # 连接数据库
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 导入数据
        success_count = 0
        error_count = 0
        error_details = []
        existing_dealers = set()
        dealer_id_map = {}  # 存储名称到ID的映射，用于处理上级经销商
        
        # 获取已存在的经销商名称和ID
        cursor.execute("SELECT id, name FROM dealer_companies")
        for row in cursor.fetchall():
            existing_dealers.add(row['name'].lower())
            dealer_id_map[row['name'].lower()] = row['id']
        
        # 第一遍处理：只创建经销商记录，不处理上级关系
        for index, row in df.iterrows():
            try:
                name = str(row['name']).strip()
                
                # 跳过空名称
                if not name or pd.isna(name):
                    error_count += 1
                    error_details.append(f"行 {index+2}: 经销商名称不能为空")
                    continue
                
                # 检查名称是否已存在
                if name.lower() in existing_dealers:
                    error_count += 1
                    error_details.append(f"行 {index+2}: 经销商 '{name}' 已存在")
                    continue
                
                # 处理可选字段
                address = str(row.get('address', '')).strip() if not pd.isna(row.get('address', '')) else None
                contact_phone = str(row.get('contact_phone', '')).strip() if not pd.isna(row.get('contact_phone', '')) else None
                
                # 处理权限字段
                can_recommend_books = bool(row.get('can_recommend_books', False)) if not pd.isna(row.get('can_recommend_books', False)) else False
                can_invite_users = bool(row.get('can_invite_users', False)) if not pd.isna(row.get('can_invite_users', False)) else False
                can_initiate_exhibition = bool(row.get('can_initiate_exhibition', False)) if not pd.isna(row.get('can_initiate_exhibition', False)) else False
                can_register_exhibition = bool(row.get('can_register_exhibition', False)) if not pd.isna(row.get('can_register_exhibition', False)) else False
                
                # 插入经销商记录，先不设置上级关系
                cursor.execute("""
                    INSERT INTO dealer_companies 
                    (name, address, contact_phone, parent_company_id, created_at) 
                    VALUES (%s, %s, %s, NULL, NOW())
                """, (name, address, contact_phone))
                
                dealer_id = cursor.lastrowid
                
                # 保存新的经销商ID到映射
                existing_dealers.add(name.lower())
                dealer_id_map[name.lower()] = dealer_id
                
                # 创建权限记录
                cursor.execute("""
                    INSERT INTO dealer_company_permissions 
                    (company_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                """, (dealer_id, can_recommend_books, can_invite_users, can_initiate_exhibition, can_register_exhibition))
                
                success_count += 1
            except Exception as e:
                error_count += 1
                error_details.append(f"行 {index+2}: {str(e)}")
        
        # 第二遍处理：更新上级经销商关系
        for index, row in df.iterrows():
            try:
                name = str(row['name']).strip()
                dealer_id = dealer_id_map.get(name.lower())
                
                # 只处理成功创建的经销商
                if not dealer_id:
                    continue
                
                # 处理上级经销商关系
                parent_name = row.get('parent_company_name', '')
                if parent_name and not pd.isna(parent_name):
                    parent_name = str(parent_name).strip()
                    parent_id = dealer_id_map.get(parent_name.lower())
                    
                    if parent_id and parent_id != dealer_id:  # 确保不是自己的上级
                        cursor.execute("UPDATE dealer_companies SET parent_company_id = %s WHERE id = %s", (parent_id, dealer_id))
            except Exception as e:
                pass
                # print(f"更新上级经销商关系失败: {str(e)}")
                # 不影响之前的成功导入，所以这里不增加error_count
        
        # 提交事务
        connection.commit()
        cursor.close()
        connection.close()
        
        # print(f"导入完成: 成功 {success_count} 条, 失败 {error_count} 条")
        return jsonify({
            "code": 0,
            "message": f"导入完成: 成功 {success_count} 条, 失败 {error_count} 条",
            "data": {
                "success_count": success_count,
                "fail_count": error_count,
                "error_details": error_details
            }
        })
    except Exception as e:
        import traceback
        error_info = traceback.format_exc()
        # print(f"导入经销商失败: {str(e)}")
        # print(f"错误详情: {error_info}")
        return jsonify({"code": 1, "message": f"导入经销商失败: {str(e)}"}), 500

# @admin_bp.route('/dashboard', methods=['GET'])
# def dashboard():
#     """
#     管理员仪表盘页面
#     """
#     if 'user_id' not in session or session.get('role') != 'admin':
#         return jsonify({"code": 1, "message": "无权限访问"})
    
#     return render_template('admin_dashboard.html')

@admin_bp.route('/get_sample_publishers', methods=['GET'])
def get_sample_publishers():
    """获取有样书的出版社列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询所有出版社及其样书数量，显示出版社公司名称
            sql = """
                SELECT u.user_id as id, 
                       COALESCE(pc.name, u.name) as name,
                       COUNT(sb.id) as sample_count
                FROM users u
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                LEFT JOIN sample_books sb ON u.user_id = sb.publisher_id
                WHERE u.role = 'publisher'
                GROUP BY u.user_id, u.name, pc.name
                ORDER BY COALESCE(pc.name, u.name)
            """
            cursor.execute(sql)
            publishers = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": publishers
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})
    finally:
        connection.close()

# 用户权限管理相关API
@admin_bp.route('/get_user_permissions', methods=['GET'])
def get_user_permissions():
    """
    获取用户权限列表
    请求参数:
        page: 页码
        per_page: 每页数量
        search: 搜索关键词（用户名或权限名称）
        permission_name: 权限名称筛选
        permission_value: 权限值筛选
    返回:
        用户权限列表
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        search = request.args.get('search', '').strip()
        permission_name = request.args.get('permission_name', '').strip()
        permission_value = request.args.get('permission_value', '').strip()

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 构建查询条件
        where_conditions = []
        params = []

        if search:
            where_conditions.append("(u.username LIKE %s OR u.name LIKE %s OR up.permission_name LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        if permission_name:
            where_conditions.append("up.permission_name = %s")
            params.append(permission_name)

        if permission_value:
            where_conditions.append("up.permission_value = %s")
            params.append(permission_value)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 查询总数
        count_sql = f"""
            SELECT COUNT(*) as total
            FROM user_permissions up
            JOIN users u ON up.user_id = u.user_id
            WHERE {where_clause}
        """
        cursor.execute(count_sql, params)
        total = cursor.fetchone()['total']

        # 查询权限列表（包含组织信息）
        offset = (page - 1) * per_page
        list_sql = f"""
            SELECT up.id, up.user_id, up.permission_name, up.permission_value,
                   up.is_enabled, up.created_at, up.updated_at, up.remark,
                   u.username, u.name, u.role,
                   creator.username as creator_name,
                   CASE
                       WHEN u.role = 'teacher' THEN s.name
                       WHEN u.role = 'publisher' THEN p.name
                       WHEN u.role = 'dealer' THEN d.name
                       ELSE NULL
                   END as org_name
            FROM user_permissions up
            JOIN users u ON up.user_id = u.user_id
            LEFT JOIN users creator ON up.created_by = creator.user_id
            LEFT JOIN schools s ON u.teacher_school_id = s.id AND u.role = 'teacher'
            LEFT JOIN publisher_companies p ON u.publisher_company_id = p.id AND u.role = 'publisher'
            LEFT JOIN dealer_companies d ON u.dealer_company_id = d.id AND u.role = 'dealer'
            WHERE {where_clause}
            ORDER BY up.created_at DESC
            LIMIT %s OFFSET %s
        """
        cursor.execute(list_sql, params + [per_page, offset])
        permissions = cursor.fetchall()

        # 格式化时间
        for permission in permissions:
            if permission['created_at']:
                permission['created_at'] = permission['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if permission['updated_at']:
                permission['updated_at'] = permission['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "permissions": permissions,
                "total": total,
                "page": page,
                "per_page": per_page,
                "total_pages": (total + per_page - 1) // per_page
            }
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取用户权限列表失败: {str(e)}"})

@admin_bp.route('/add_user_permission', methods=['POST'])
def add_user_permission():
    """
    添加或修改用户权限
    请求参数:
        user_id: 用户ID
        permission_name: 权限名称
        permission_value: 权限值（allow/deny）
        is_enabled: 是否启用（1/0）
        remark: 备注
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.json
        user_id = data.get('user_id')
        permission_name = data.get('permission_name', '').strip()
        permission_value = data.get('permission_value', 'deny').strip()
        is_enabled = int(data.get('is_enabled', 1))
        remark = data.get('remark', '').strip()

        if not user_id or not permission_name:
            return jsonify({"code": 1, "message": "用户ID和权限名称不能为空"})

        if permission_value not in ['allow', 'deny']:
            return jsonify({"code": 1, "message": "权限值只能是allow或deny"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查用户是否存在
        cursor.execute("SELECT user_id FROM users WHERE user_id = %s", (user_id,))
        if not cursor.fetchone():
            return jsonify({"code": 1, "message": "用户不存在"})

        # 检查权限是否已存在
        cursor.execute("""
            SELECT id FROM user_permissions
            WHERE user_id = %s AND permission_name = %s
        """, (user_id, permission_name))
        existing = cursor.fetchone()

        if existing:
            # 更新现有权限
            cursor.execute("""
                UPDATE user_permissions
                SET permission_value = %s, is_enabled = %s, remark = %s, updated_at = NOW()
                WHERE user_id = %s AND permission_name = %s
            """, (permission_value, is_enabled, remark, user_id, permission_name))
            message = "权限更新成功"
        else:
            # 添加新权限
            cursor.execute("""
                INSERT INTO user_permissions (user_id, permission_name, permission_value, is_enabled, created_by, remark)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (user_id, permission_name, permission_value, is_enabled, session['user_id'], remark))
            message = "权限添加成功"

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": message})
    except Exception as e:
        return jsonify({"code": 1, "message": f"操作失败: {str(e)}"})

@admin_bp.route('/delete_user_permission', methods=['POST'])
def delete_user_permission():
    """
    删除用户权限
    请求参数:
        permission_id: 权限记录ID
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.json
        permission_id = data.get('permission_id')

        if not permission_id:
            return jsonify({"code": 1, "message": "权限ID不能为空"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查权限是否存在
        cursor.execute("SELECT id FROM user_permissions WHERE id = %s", (permission_id,))
        if not cursor.fetchone():
            return jsonify({"code": 1, "message": "权限记录不存在"})

        # 删除权限
        cursor.execute("DELETE FROM user_permissions WHERE id = %s", (permission_id,))
        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "权限删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除失败: {str(e)}"})

@admin_bp.route('/get_permission_options', methods=['GET'])
def get_permission_options():
    """
    获取权限选项（用于下拉框）
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 预定义的权限选项
        permission_options = [
            {
                'value': 'view_rate_info',
                'label': '查看费率信息',
                'description': '控制用户是否可以查看产品的价格、折扣、费率等信息'
            }
            # 后续可以添加更多权限
        ]

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": permission_options
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取权限选项失败: {str(e)}"})

@admin_bp.route('/get_organizations', methods=['GET'])
def get_organizations():
    """
    获取组织列表
    请求参数:
        type: 组织类型（school/publisher/dealer）
        search: 搜索关键词
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        org_type = request.args.get('type', '').strip()
        search = request.args.get('search', '').strip()

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        organizations = []

        # 根据类型查询不同的组织表
        if not org_type or org_type == 'school':
            # 查询学校及其用户数量
            where_conditions = []
            params = []

            if search:
                where_conditions.append("s.name LIKE %s")
                params.append(f"%{search}%")

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            cursor.execute(f"""
                SELECT s.id, s.name, 'school' as type,
                       COALESCE(COUNT(u.user_id), 0) as user_count
                FROM schools s
                LEFT JOIN users u ON s.id = u.teacher_school_id AND u.role = 'teacher'
                WHERE {where_clause}
                GROUP BY s.id, s.name
                ORDER BY user_count DESC, s.name
            """, params)

            schools = cursor.fetchall()
            for school in schools:
                organizations.append({
                    'id': school['id'],
                    'name': school['name'],
                    'type': 'school',
                    'user_count': school['user_count']
                })

        if not org_type or org_type == 'publisher':
            # 查询出版社及其用户数量
            where_conditions = []
            params = []

            if search:
                where_conditions.append("p.name LIKE %s")
                params.append(f"%{search}%")

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            cursor.execute(f"""
                SELECT p.id, p.name, 'publisher' as type,
                       COALESCE(COUNT(u.user_id), 0) as user_count
                FROM publisher_companies p
                LEFT JOIN users u ON p.id = u.publisher_company_id AND u.role = 'publisher'
                WHERE {where_clause}
                GROUP BY p.id, p.name
                ORDER BY user_count DESC, p.name
            """, params)

            publishers = cursor.fetchall()
            for publisher in publishers:
                organizations.append({
                    'id': publisher['id'],
                    'name': publisher['name'],
                    'type': 'publisher',
                    'user_count': publisher['user_count']
                })

        if not org_type or org_type == 'dealer':
            # 查询经销商及其用户数量
            where_conditions = []
            params = []

            if search:
                where_conditions.append("d.name LIKE %s")
                params.append(f"%{search}%")

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            cursor.execute(f"""
                SELECT d.id, d.name, 'dealer' as type,
                       COALESCE(COUNT(u.user_id), 0) as user_count
                FROM dealer_companies d
                LEFT JOIN users u ON d.id = u.dealer_company_id AND u.role = 'dealer'
                WHERE {where_clause}
                GROUP BY d.id, d.name
                ORDER BY user_count DESC, d.name
            """, params)

            dealers = cursor.fetchall()
            for dealer in dealers:
                organizations.append({
                    'id': dealer['id'],
                    'name': dealer['name'],
                    'type': 'dealer',
                    'user_count': dealer['user_count']
                })

        cursor.close()
        connection.close()

        # 按用户数量从多到少排序
        organizations.sort(key=lambda x: x['user_count'], reverse=True)

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": organizations
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取组织列表失败: {str(e)}"})

@admin_bp.route('/get_users_by_org', methods=['GET'])
def get_users_by_org():
    """
    根据组织获取用户列表
    请求参数:
        org_type: 组织类型（school/publisher/dealer）
        org_id: 组织ID
        search: 搜索关键词（用户名或姓名）
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        org_type = request.args.get('org_type', '').strip()
        org_id = request.args.get('org_id', '').strip()
        search = request.args.get('search', '').strip()

        if not org_type or not org_id:
            return jsonify({"code": 1, "message": "组织类型和ID不能为空"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 构建查询条件
        where_conditions = []
        params = []

        # 根据组织类型设置查询条件
        if org_type == 'school':
            where_conditions.append("teacher_school_id = %s")
            params.append(org_id)
            where_conditions.append("role = 'teacher'")
        elif org_type == 'publisher':
            where_conditions.append("publisher_company_id = %s")
            params.append(org_id)
            where_conditions.append("role = 'publisher'")
        elif org_type == 'dealer':
            where_conditions.append("dealer_company_id = %s")
            params.append(org_id)
            where_conditions.append("role = 'dealer'")
        else:
            return jsonify({"code": 1, "message": "无效的组织类型"})

        # 添加搜索条件
        if search:
            where_conditions.append("(username LIKE %s OR name LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param])

        where_clause = " AND ".join(where_conditions)

        # 查询用户及其组织信息
        if org_type == 'school':
            cursor.execute(f"""
                SELECT u.user_id, u.username, u.name, u.role, u.email, u.phone_number,
                       s.name as org_name
                FROM users u
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                WHERE {where_clause}
                ORDER BY u.name, u.username
            """, params)
        elif org_type == 'publisher':
            cursor.execute(f"""
                SELECT u.user_id, u.username, u.name, u.role, u.email, u.phone_number,
                       p.name as org_name
                FROM users u
                LEFT JOIN publisher_companies p ON u.publisher_company_id = p.id
                WHERE {where_clause}
                ORDER BY u.name, u.username
            """, params)
        elif org_type == 'dealer':
            cursor.execute(f"""
                SELECT u.user_id, u.username, u.name, u.role, u.email, u.phone_number,
                       d.name as org_name
                FROM users u
                LEFT JOIN dealer_companies d ON u.dealer_company_id = d.id
                WHERE {where_clause}
                ORDER BY u.name, u.username
            """, params)
        else:
            cursor.execute(f"""
                SELECT user_id, username, name, role, email, phone_number,
                       NULL as org_name
                FROM users
                WHERE {where_clause}
                ORDER BY name, username
            """, params)

        users = cursor.fetchall()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": users
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取用户列表失败: {str(e)}"})

def check_user_permission(user_id, permission_name):
    """
    检查用户是否有指定权限
    参数:
        user_id: 用户ID
        permission_name: 权限名称
    返回:
        True: 有权限（允许）
        False: 无权限（禁止或未设置）
    """
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询用户权限
        cursor.execute("""
            SELECT permission_value, is_enabled
            FROM user_permissions
            WHERE user_id = %s AND permission_name = %s
        """, (user_id, permission_name))

        permission = cursor.fetchone()
        cursor.close()
        connection.close()

        # 如果没有设置权限，默认允许（兼容现有用户）
        if not permission:
            return True

        # 如果权限被禁用，默认允许
        if not permission['is_enabled']:
            return True

        # 返回权限值（allow为True，deny为False）
        return permission['permission_value'] == 'allow'
    except Exception as e:
        print(f"检查用户权限失败: {str(e)}")
        # 出错时默认允许，避免影响正常功能
        return True

@admin_bp.route('/get_sample_directories', methods=['GET'])
def get_sample_directories():
    """获取指定出版社的目录结构"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    publisher_id = request.args.get('publisher_id')
    if not publisher_id:
        return jsonify({"code": 1, "message": "出版社ID为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询该出版社的所有目录
            sql = """
                SELECT d.id, d.name, d.parent_id, COUNT(sb.id) as sample_count
                FROM directories d
                LEFT JOIN sample_books sb ON d.id = sb.parent_id
                WHERE d.publisher_id = %s
                GROUP BY d.id
                ORDER BY d.name
            """
            cursor.execute(sql, (publisher_id,))
            directories = cursor.fetchall()

            # 查询总样书数量
            sql = """
                SELECT COUNT(*) as total
                FROM sample_books
                WHERE publisher_id = %s
            """
            cursor.execute(sql, (publisher_id,))
            total_result = cursor.fetchone()
            total_samples = total_result['total'] if total_result else 0

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": directories,
                "total_samples": total_samples
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取目录失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_admin_samples', methods=['GET'])
def get_admin_samples():
    """获取指定出版社的样书列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    publisher_id = request.args.get('publisher_id')
    directory_id = request.args.get('directory_id')
    search = request.args.get('search', '')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))

    if not publisher_id:
        return jsonify({"code": 1, "message": "出版社ID为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            where_clause = "sb.publisher_id = %s"
            params = [publisher_id]

            if directory_id:
                where_clause += " AND sb.parent_id = %s"
                params.append(directory_id)

            if search:
                where_clause += " AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)"
                search_param = f'%{search}%'
                params.extend([search_param, search_param, search_param])

            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM sample_books sb
                WHERE {where_clause}
            """
            cursor.execute(count_sql, params)
            total_result = cursor.fetchone()
            total = total_result['total'] if total_result else 0

            # 查询样书列表
            offset = (page - 1) * limit
            sql = f"""
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info,
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.color_system, sb.publisher_name,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                       sb.publication_date,
                       d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE {where_clause}
                ORDER BY sb.name
                LIMIT %s OFFSET %s
            """
            params.extend([limit, offset])
            cursor.execute(sql, params)
            samples = cursor.fetchall()

            # 格式化日期字段
            for sample in samples:
                if sample.get('publication_date'):
                    sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "samples": samples,
                    "total": total
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_admin_sample_detail', methods=['GET'])
def get_admin_sample_detail():
    """获取样书详情"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    sample_id = request.args.get('sample_id')
    if not sample_id:
        return jsonify({"code": 1, "message": "样书ID为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT sb.*, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE sb.id = %s
            """
            cursor.execute(sql, (sample_id,))
            sample = cursor.fetchone()

            if not sample:
                return jsonify({"code": 1, "message": "样书不存在"})

            # 格式化日期字段
            if sample.get('publication_date'):
                sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": sample
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书详情失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/export_admin_samples', methods=['GET'])
def export_admin_samples():
    """导出样书数据"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    publisher_id = request.args.get('publisher_id')
    directory_id = request.args.get('directory_id')
    search = request.args.get('search', '')

    if not publisher_id:
        return jsonify({"code": 1, "message": "出版社ID为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            where_clause = "sb.publisher_id = %s"
            params = [publisher_id]

            if directory_id:
                where_clause += " AND sb.parent_id = %s"
                params.append(directory_id)

            if search:
                where_clause += " AND (sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)"
                search_param = f'%{search}%'
                params.extend([search_param, search_param, search_param])

            # 查询样书完整信息
            sql = f"""
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info,
                       sb.discount_info, d.name as directory_name, sb.publisher_name,
                       sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       sb.material_type, sb.color_system, sb.courseware,
                       sb.sample_download_url, sb.online_reading_url,
                       sb.resources, sb.resource_download_url, sb.courseware_download_url,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                       sb.publication_date,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE {where_clause}
                ORDER BY sb.name
            """
            cursor.execute(sql, params)
            samples = cursor.fetchall()

            # 获取出版社公司名称
            cursor.execute("""
                SELECT pc.name
                FROM users u
                JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                WHERE u.user_id = %s
            """, (publisher_id,))
            publisher_result = cursor.fetchone()
            publisher_name = publisher_result['name'] if publisher_result else '未知出版社'

            # 创建Excel文件
            import pandas as pd
            import io
            from flask import send_file

            # 准备数据
            data = []
            for sample in samples:
                # 格式化出版时间
                publication_date = ''
                if sample['publication_date']:
                    publication_date = sample['publication_date'].strftime('%Y-%m-%d') if hasattr(sample['publication_date'], 'strftime') else str(sample['publication_date'])

                data.append({
                    '样书名称': sample['name'],
                    '作者': sample['author'],
                    'ISBN': sample['isbn'],
                    '价格': sample['price'],
                    '出版时间': publication_date,
                    '出版社': sample['publisher_name'],
                    '目录': sample['directory_name'],
                    '学段': sample['level'],
                    '书籍类型': sample['book_type'],
                    '材质': sample['material_type'],
                    '色系': sample['color_system'],
                    '国家规划': sample['national_regulation_level_name'] or '',
                    '省级规划': sample['provincial_regulation_level_name'] or '',
                    '特色标签': sample['feature_name'],
                    '获奖信息': sample['award_info'],
                    '课件': sample['courseware'],
                    '资源': sample['resources'],
                    '样书下载链接': sample['sample_download_url'],
                    '在线阅读链接': sample['online_reading_url'],
                    '资源下载链接': sample['resource_download_url'],
                    '课件下载链接': sample['courseware_download_url'],
                    '发货折扣': sample['shipping_discount'],
                    '结算折扣': sample['settlement_discount'],
                    '推广费率': sample['promotion_rate']
                })

            # 创建DataFrame
            df = pd.DataFrame(data)

            # 创建Excel文件
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='样书数据', index=False)

                # 设置列宽
                worksheet = writer.sheets['样书数据']
                for i, col in enumerate(df.columns):
                    max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
                    worksheet.set_column(i, i, min(max_len, 50))

            output.seek(0)

            # 生成文件名
            filename = f'{publisher_name}_样书数据.xlsx'

            return send_file(
                output,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

    except Exception as e:
        return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})
    finally:
        connection.close()

# ==================== 管理员报备管理功能 ====================

@admin_bp.route('/get_all_reports', methods=['GET'])
def get_all_reports():
    """获取所有报备申请"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    status = request.args.get('status', '')
    search = request.args.get('search', '')
    dealer_id = request.args.get('dealer_id', '')
    publisher_id = request.args.get('publisher_id', '')

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            if status:
                where_conditions.append("pr.status = %s")
                params.append(status)

            if search:
                where_conditions.append("(sb.name LIKE %s OR sb.author LIKE %s OR pr.school_name LIKE %s OR sb.isbn LIKE %s)")
                search_param = f'%{search}%'
                params.extend([search_param, search_param, search_param, search_param])

            if dealer_id:
                where_conditions.append("pr.dealer_id = %s")
                params.append(dealer_id)

            if publisher_id:
                where_conditions.append("sb.publisher_id = %s")
                params.append(publisher_id)

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            # 查询报备列表
            offset = (page - 1) * page_size
            sql = f"""
                SELECT pr.id, pr.dealer_id, pr.sample_book_id, pr.school_name,
                       pr.status, pr.created_at, pr.updated_at, pr.conflict_reason,
                       pr.attachment, pr.reason, pr.expiry_date, pr.promotion_status,
                       sb.name as sample_name, sb.isbn, sb.author, sb.price, sb.publisher_name,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                       u.name as dealer_name, u.phone_number as dealer_phone,
                       dc.name as dealer_company_name,
                       pu.name as publisher_user_name,
                       pc.name as publisher_company_name
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                LEFT JOIN users pu ON sb.publisher_id = pu.user_id
                LEFT JOIN publisher_companies pc ON pu.publisher_company_id = pc.id
                {where_clause}
                ORDER BY pr.created_at DESC
                LIMIT %s OFFSET %s
            """

            cursor.execute(sql, params + [page_size, offset])
            reports = cursor.fetchall()

            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                LEFT JOIN users pu ON sb.publisher_id = pu.user_id
                LEFT JOIN publisher_companies pc ON pu.publisher_company_id = pc.id
                {where_clause}
            """

            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']

            # 格式化日期
            for report in reports:
                if report['created_at']:
                    report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if report['updated_at']:
                    report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
                if report['expiry_date']:
                    report['expiry_date'] = report['expiry_date'].strftime('%Y-%m-%d')

            return jsonify({
                "code": 0,
                "data": reports,
                "total": total,
                "page": page,
                "page_size": page_size
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备列表失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_report_detail/<int:report_id>', methods=['GET'])
def get_report_detail(report_id):
    """获取报备详情"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT pr.id, pr.dealer_id, pr.sample_book_id, pr.school_name,
                       pr.status, pr.created_at, pr.updated_at, pr.conflict_reason,
                       pr.attachment, pr.reason, pr.expiry_date, pr.promotion_status,
                       sb.name as sample_name, sb.isbn, sb.author, sb.price, sb.publisher_name,
                       sb.shipping_discount, sb.settlement_discount, sb.promotion_rate,
                       sb.level, sb.book_type, sb.material_type, sb.color_system,
                       sb.national_regulation, sb.provincial_regulation,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       u.name as dealer_name, u.phone_number as dealer_phone,
                       dc.name as dealer_company_name,
                       pu.name as publisher_user_name,
                       pc.name as publisher_company_name
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                JOIN users u ON pr.dealer_id = u.user_id
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                LEFT JOIN users pu ON sb.publisher_id = pu.user_id
                LEFT JOIN publisher_companies pc ON pu.publisher_company_id = pc.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE pr.id = %s
            """

            cursor.execute(sql, (report_id,))
            report = cursor.fetchone()

            if not report:
                return jsonify({"code": 1, "message": "报备不存在"})

            # 格式化日期
            if report['created_at']:
                report['created_at'] = report['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report['updated_at']:
                report['updated_at'] = report['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            if report['expiry_date']:
                report['expiry_date'] = report['expiry_date'].strftime('%Y-%m-%d')

            # 获取样书特色
            cursor.execute("""
                SELECT bf.name
                FROM sample_book_features sbf
                JOIN book_features bf ON sbf.feature_id = bf.id
                WHERE sbf.sample_id = %s
            """, (report['sample_book_id'],))
            features = cursor.fetchall()
            report['features'] = [f['name'] for f in features]

            return jsonify({
                "code": 0,
                "data": report
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报备详情失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/update_report_status', methods=['POST'])
def update_report_status():
    """更新报备状态"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    report_id = request.form.get('report_id')
    status = request.form.get('status')
    reason = request.form.get('reason', '')
    promotion_status = request.form.get('promotion_status', '')

    if not report_id or not status:
        return jsonify({"code": 1, "message": "报备ID和状态为必填项"})

    if status not in ['pending', 'approved', 'rejected', 'completed']:
        return jsonify({"code": 1, "message": "无效的状态值"})

    # 如果状态为拒绝，必须填写拒绝原因
    if status == 'rejected' and not reason.strip():
        return jsonify({"code": 1, "message": "拒绝状态必须填写拒绝原因"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取当前报备信息
            cursor.execute("""
                SELECT sample_book_id, school_name, status as current_status
                FROM promotion_reports
                WHERE id = %s
            """, (report_id,))
            current_report = cursor.fetchone()

            if not current_report:
                return jsonify({"code": 1, "message": "报备不存在"})

            conflict_reason = ''

            # 如果状态改为已通过，检查是否有冲突
            if status == 'approved' and current_report['current_status'] != 'approved':
                # 检查相同样书、相同学校、已通过、在有效期内的报备
                conflict_sql = """
                    SELECT id, dealer_id, expiry_date
                    FROM promotion_reports
                    WHERE sample_book_id = %s
                    AND school_name = %s
                    AND status = 'approved'
                    AND id != %s
                    AND (expiry_date IS NULL OR expiry_date >= CURDATE())
                """
                cursor.execute(conflict_sql, (current_report['sample_book_id'], current_report['school_name'], report_id))
                conflicting_reports = cursor.fetchall()

                if conflicting_reports:
                    # 将冲突的报备设为拒绝
                    for conflict_report in conflicting_reports:
                        update_sql = """
                            UPDATE promotion_reports
                            SET status = 'rejected',
                                conflict_reason = %s,
                                updated_at = NOW()
                            WHERE id = %s
                        """
                        conflict_reason_text = f"因报备ID {report_id} 通过而自动拒绝"
                        cursor.execute(update_sql, (conflict_reason_text, conflict_report['id']))

                    conflict_reason = f"检测到 {len(conflicting_reports)} 个冲突报备，已自动处理"

            # 更新报备状态
            update_fields = ['status = %s', 'updated_at = NOW()']
            update_params = [status]

            if reason:
                update_fields.append('reason = %s')
                update_params.append(reason)
            elif status != 'rejected':
                # 如果不是拒绝状态，清空拒绝原因
                update_fields.append('reason = %s')
                update_params.append('')

            if conflict_reason:
                update_fields.append('conflict_reason = %s')
                update_params.append(conflict_reason)

            if promotion_status:
                update_fields.append('promotion_status = %s')
                update_params.append(promotion_status)

            update_params.append(report_id)

            sql = f"""
                UPDATE promotion_reports
                SET {', '.join(update_fields)}
                WHERE id = %s
            """
            cursor.execute(sql, update_params)
            connection.commit()

            message = f"报备状态已更新为{status}"
            if conflict_reason:
                message += f"，{conflict_reason}"

            return jsonify({
                "code": 0,
                "message": message
            })

    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"更新报备状态失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/add_report', methods=['POST'])
def add_report():
    """管理员添加报备"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    dealer_id = request.form.get('dealer_id')
    sample_book_id = request.form.get('sample_book_id')
    school_name = request.form.get('school_name')
    status = request.form.get('status', 'pending')
    reason = request.form.get('reason', '')
    expiry_date = request.form.get('expiry_date', '')
    promotion_status = request.form.get('promotion_status', 'pending')

    if not dealer_id or not sample_book_id or not school_name:
        return jsonify({"code": 1, "message": "经销商、样书和学校名称为必填项"})

    # 如果状态为拒绝，必须填写拒绝原因
    if status == 'rejected' and not reason.strip():
        return jsonify({"code": 1, "message": "拒绝状态必须填写拒绝原因"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证经销商和样书是否存在
            cursor.execute("SELECT user_id FROM users WHERE user_id = %s AND role = 'dealer'", (dealer_id,))
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "经销商不存在"})

            cursor.execute("SELECT id FROM sample_books WHERE id = %s", (sample_book_id,))
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "样书不存在"})

            conflict_reason = ''

            # 如果状态为已通过，检查是否有冲突
            if status == 'approved':
                # 检查相同样书、相同学校、已通过、在有效期内的报备
                conflict_sql = """
                    SELECT id, dealer_id, expiry_date
                    FROM promotion_reports
                    WHERE sample_book_id = %s
                    AND school_name = %s
                    AND status = 'approved'
                    AND (expiry_date IS NULL OR expiry_date >= CURDATE())
                """
                cursor.execute(conflict_sql, (sample_book_id, school_name))
                conflicting_reports = cursor.fetchall()

                if conflicting_reports:
                    # 将冲突的报备设为拒绝
                    for conflict_report in conflicting_reports:
                        update_sql = """
                            UPDATE promotion_reports
                            SET status = 'rejected',
                                conflict_reason = %s,
                                updated_at = NOW()
                            WHERE id = %s
                        """
                        conflict_reason_text = f"因新报备通过而自动拒绝（冲突报备ID: {conflict_report['id']}）"
                        cursor.execute(update_sql, (conflict_reason_text, conflict_report['id']))

                    conflict_reason = f"检测到 {len(conflicting_reports)} 个冲突报备，已自动处理"

            # 插入报备记录
            sql = """
                INSERT INTO promotion_reports
                (dealer_id, sample_book_id, school_name, status, reason, conflict_reason, expiry_date, promotion_status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (dealer_id, sample_book_id, school_name, status, reason, conflict_reason,
                                expiry_date if expiry_date else None, promotion_status))
            connection.commit()

            message = "报备添加成功"
            if conflict_reason:
                message += f"，{conflict_reason}"

            return jsonify({
                "code": 0,
                "message": message
            })

    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"添加报备失败: {str(e)}"})
    finally:
        connection.close()

# ==================== 网站设置管理 ====================

@admin_bp.route('/get_site_settings', methods=['GET'])
def get_site_settings():
    """
    获取网站设置列表
    返回:
        网站设置列表
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询所有网站设置
        sql = """
            SELECT id, user_role, site_name, login_url, logo_url, is_active,
                   created_at, updated_at
            FROM site_settings
            ORDER BY
                CASE user_role
                    WHEN 'default' THEN 1
                    WHEN 'admin' THEN 2
                    WHEN 'teacher' THEN 3
                    WHEN 'publisher' THEN 4
                    WHEN 'dealer' THEN 5
                    ELSE 6
                END
        """
        cursor.execute(sql)
        settings = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": settings
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取网站设置失败: {str(e)}"})

@admin_bp.route('/get_site_setting/<role>', methods=['GET'])
def get_site_setting(role):
    """
    获取指定角色的网站设置
    参数:
        role: 用户角色
    返回:
        网站设置详情
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询指定角色的网站设置
        sql = """
            SELECT id, user_role, site_name, login_url, logo_url, is_active,
                   created_at, updated_at
            FROM site_settings
            WHERE user_role = %s
        """
        cursor.execute(sql, (role,))
        setting = cursor.fetchone()

        cursor.close()
        connection.close()

        if setting:
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": setting
            })
        else:
            return jsonify({"code": 1, "message": "未找到该角色的网站设置"})

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取网站设置失败: {str(e)}"})

@admin_bp.route('/update_site_setting', methods=['PUT'])
def update_site_setting():
    """
    更新网站设置
    请求参数:
        id: 设置ID
        site_name: 网站名称
        login_url: 登录URL
        logo_url: logo路径
        is_active: 是否启用
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()

        # 必填字段验证
        setting_id = data.get('id')
        site_name = data.get('site_name')

        if not setting_id or not site_name:
            return jsonify({"code": 1, "message": "设置ID和网站名称为必填项"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查设置是否存在
        check_sql = "SELECT id FROM site_settings WHERE id = %s"
        cursor.execute(check_sql, (setting_id,))
        existing = cursor.fetchone()

        if not existing:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "网站设置不存在"})

        # 更新设置
        sql = """
            UPDATE site_settings
            SET site_name = %s, login_url = %s, logo_url = %s, is_active = %s
            WHERE id = %s
        """
        cursor.execute(sql, (
            site_name,
            data.get('login_url', ''),
            data.get('logo_url', ''),
            data.get('is_active', 1),
            setting_id
        ))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "网站设置更新成功"
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"更新网站设置失败: {str(e)}"})


@admin_bp.route('/upload_logo', methods=['POST'])
def upload_logo():
    """
    上传logo文件
    返回:
        文件路径
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        if 'logo' not in request.files:
            return jsonify({"code": 1, "message": "未选择文件"})

        file = request.files['logo']
        if file.filename == '':
            return jsonify({"code": 1, "message": "未选择文件"})

        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        if '.' not in file.filename or file.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
            return jsonify({"code": 1, "message": "不支持的文件格式，请上传 PNG、JPG、JPEG、GIF 或 WebP 格式的图片"})

        # 检查文件大小（5MB限制）
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头

        if file_size > 5 * 1024 * 1024:  # 5MB
            return jsonify({"code": 1, "message": "文件大小不能超过5MB"})

        # 生成唯一文件名
        import uuid
        import os
        from werkzeug.utils import secure_filename

        filename = secure_filename(file.filename)
        file_ext = filename.rsplit('.', 1)[1].lower()
        unique_filename = f"logo_{uuid.uuid4().hex}.{file_ext}"

        # 确保上传目录存在
        upload_dir = os.path.join('app', 'static', 'upload', 'logos')
        os.makedirs(upload_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # 返回相对路径
        relative_path = f"/static/upload/logos/{unique_filename}"

        return jsonify({
            "code": 0,
            "message": "文件上传成功",
            "data": {
                "logo_url": relative_path
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"文件上传失败: {str(e)}"})

@admin_bp.route('/site_settings', methods=['GET'])
def site_settings_page():
    """
    渲染网站设置管理页面
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    return render_template('pc_admin_site_settings.html')

@admin_bp.route('/delete_report/<int:report_id>', methods=['DELETE'])
def delete_report(report_id):
    """删除报备"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查报备是否存在
            cursor.execute("SELECT id FROM promotion_reports WHERE id = %s", (report_id,))
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "报备不存在"})

            # 删除报备
            cursor.execute("DELETE FROM promotion_reports WHERE id = %s", (report_id,))
            connection.commit()

            return jsonify({
                "code": 0,
                "message": "报备删除成功"
            })

    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"删除报备失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_dealers_for_report', methods=['GET'])
def get_dealers_for_report():
    """获取经销商列表用于报备"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    search = request.args.get('search', '')

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            where_clause = ""
            params = []

            if search:
                where_clause = "WHERE (u.name LIKE %s OR dc.name LIKE %s)"
                search_param = f'%{search}%'
                params = [search_param, search_param]

            sql = f"""
                SELECT u.user_id as id, u.name, u.phone_number, dc.name as company_name
                FROM users u
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                WHERE u.role = 'dealer' {' AND ' + where_clause.replace('WHERE ', '') if where_clause else ''}
                ORDER BY u.name
            """

            cursor.execute(sql, params)
            dealers = cursor.fetchall()

            return jsonify({
                "code": 0,
                "data": dealers
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取经销商列表失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_samples_for_report', methods=['GET'])
def get_samples_for_report():
    """获取样书列表用于报备"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    search = request.args.get('search', '')
    publisher_id = request.args.get('publisher_id', '')

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            where_conditions = []
            params = []

            if search:
                where_conditions.append("(sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)")
                search_param = f'%{search}%'
                params.extend([search_param, search_param, search_param])

            if publisher_id:
                where_conditions.append("sb.publisher_id = %s")
                params.append(publisher_id)

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            sql = f"""
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.publisher_name,
                       u.name as publisher_user_name,
                       pc.name as publisher_company_name
                FROM sample_books sb
                LEFT JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                {where_clause}
                ORDER BY sb.name
                LIMIT 50
            """

            cursor.execute(sql, params)
            samples = cursor.fetchall()

            return jsonify({
                "code": 0,
                "data": samples
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书列表失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_publishers_for_report', methods=['GET'])
def get_publishers_for_report():
    """获取出版社列表用于报备筛选"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT DISTINCT u.user_id as id,
                       COALESCE(pc.name, u.name) as name
                FROM users u
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                WHERE u.role = 'publisher'
                ORDER BY name
            """

            cursor.execute(sql)
            publishers = cursor.fetchall()

            return jsonify({
                "code": 0,
                "data": publishers
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_report_status_counts', methods=['GET'])
def get_report_status_counts():
    """获取报备状态统计"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                FROM promotion_reports
            """

            cursor.execute(sql)
            result = cursor.fetchone()

            return jsonify({
                "code": 0,
                "data": {
                    "total": result['total'],
                    "pending": result['pending'],
                    "approved": result['approved'],
                    "rejected": result['rejected'],
                    "completed": result['completed']
                }
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取状态统计失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/update_sample', methods=['POST'])
def update_sample():
    """管理员更新样书"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    sample_id = request.form.get('sample_id')
    if not sample_id:
        return jsonify({"code": 1, "message": "样书ID为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查样书是否存在
            cursor.execute("SELECT id FROM sample_books WHERE id = %s", (sample_id,))
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "样书不存在"})

            # 构建更新字段
            update_fields = []
            params = []

            # 基本信息字段
            fields = ['name', 'author', 'isbn', 'price', 'level', 'book_type',
                     'material_type', 'color_system', 'awards',
                     'shipping_discount', 'settlement_discount', 'promotion_rate',
                     'publisher_name', 'courseware', 'resources',
                     'sample_download_url', 'online_reading_url',
                     'resource_download_url', 'courseware_download_url',
                     'publication_date']

            for field in fields:
                value = request.form.get(field)
                if value is not None:  # 允许空字符串
                    # 特殊处理awards字段，映射到award_info
                    db_field = 'award_info' if field == 'awards' else field
                    update_fields.append(f"{db_field} = %s")
                    params.append(value if value != '' else None)

            # 处理规划信息
            national_regulation = request.form.get('national_regulation')
            if national_regulation is not None:
                update_fields.append("national_regulation = %s")
                params.append(1 if national_regulation == '1' else 0)

            national_regulation_level_id = request.form.get('national_regulation_level_id')
            if national_regulation_level_id:
                update_fields.append("national_regulation_level_id = %s")
                params.append(national_regulation_level_id)

            provincial_regulation = request.form.get('provincial_regulation')
            if provincial_regulation is not None:
                update_fields.append("provincial_regulation = %s")
                params.append(1 if provincial_regulation == '1' else 0)

            provincial_regulation_level_id = request.form.get('provincial_regulation_level_id')
            if provincial_regulation_level_id:
                update_fields.append("provincial_regulation_level_id = %s")
                params.append(provincial_regulation_level_id)

            # 处理特色标签
            feature_ids_json = request.form.get('feature_ids')
            if feature_ids_json:
                try:
                    import json
                    feature_ids = json.loads(feature_ids_json)

                    # 删除现有的特色关联
                    cursor.execute("DELETE FROM sample_book_features WHERE sample_id = %s", (sample_id,))

                    # 添加新的特色关联
                    if feature_ids:
                        for feature_id in feature_ids:
                            cursor.execute(
                                "INSERT INTO sample_book_features (sample_id, feature_id) VALUES (%s, %s)",
                                (sample_id, feature_id)
                            )
                except (json.JSONDecodeError, ValueError):
                    pass  # 忽略JSON解析错误

            # 处理封面文件上传
            if 'cover_file' in request.files:
                cover_file = request.files['cover_file']
                if cover_file and cover_file.filename:
                    try:
                        import os
                        import uuid

                        # 检查文件类型
                        allowed_extensions = {'jpg', 'jpeg', 'png', 'webp'}
                        file_ext = cover_file.filename.rsplit('.', 1)[1].lower() if '.' in cover_file.filename else ''

                        if file_ext in allowed_extensions:
                            # 生成唯一文件名
                            unique_filename = f"{uuid.uuid4().hex}.{file_ext}"

                            # 确保上传目录存在
                            upload_dir = os.path.join('app', 'static', 'uploads', 'covers')
                            os.makedirs(upload_dir, exist_ok=True)

                            # 保存文件
                            file_path = os.path.join(upload_dir, unique_filename)
                            cover_file.save(file_path)

                            # 更新数据库中的附件链接
                            attachment_url = f"/static/uploads/covers/{unique_filename}"
                            update_fields.append("attachment_link = %s")
                            params.append(attachment_url)
                    except Exception as e:
                        print(f"文件上传失败: {str(e)}")
                        # 文件上传失败不影响其他字段的更新

            if not update_fields:
                return jsonify({"code": 1, "message": "没有要更新的字段"})

            params.append(sample_id)

            # 执行更新
            sql = f"UPDATE sample_books SET {', '.join(update_fields)} WHERE id = %s"
            cursor.execute(sql, params)
            connection.commit()

            return jsonify({
                "code": 0,
                "message": "样书更新成功"
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新样书失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/delete_sample', methods=['POST'])
def delete_sample():
    """管理员删除样书"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    sample_id = request.form.get('sample_id')
    if not sample_id:
        return jsonify({"code": 1, "message": "样书ID为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查样书是否存在
            cursor.execute("SELECT id, name FROM sample_books WHERE id = %s", (sample_id,))
            sample = cursor.fetchone()
            if not sample:
                return jsonify({"code": 1, "message": "样书不存在"})

            # 删除相关的特色标签关联
            cursor.execute("DELETE FROM sample_book_features WHERE sample_id = %s", (sample_id,))

            # 删除样书
            cursor.execute("DELETE FROM sample_books WHERE id = %s", (sample_id,))
            connection.commit()

            return jsonify({
                "code": 0,
                "message": f"样书 '{sample['name']}' 删除成功"
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除样书失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_levels', methods=['GET'])
def get_levels():
    """管理员获取学校层次列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    # 直接返回预定义的学校层次，与数据库枚举值一致
    predefined_levels = [
        {"id": 1, "name": "中职"},
        {"id": 2, "name": "专科"},
        {"id": 3, "name": "本科"},
        {"id": 4, "name": "技校"}
    ]

    return jsonify({
        "code": 0,
        "data": predefined_levels
    })

@admin_bp.route('/get_book_types', methods=['GET'])
def get_book_types():
    """管理员获取图书类型列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 从样书表中获取已有的图书类型
            cursor.execute("SELECT DISTINCT book_type as name FROM sample_books WHERE book_type IS NOT NULL ORDER BY book_type")
            db_types = cursor.fetchall()

            # 转换为标准格式
            types = [{"id": i+1, "name": row["name"]} for i, row in enumerate(db_types)]

            return jsonify({
                "code": 0,
                "data": types
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取图书类型数据失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_national_regulation_levels', methods=['GET'])
def get_national_regulation_levels():
    """管理员获取国家规划级别列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT id, name FROM national_regulation_levels ORDER BY id")
            levels = cursor.fetchall()
            return jsonify({
                "code": 0,
                "data": levels
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取国家规划级别数据失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_provincial_regulation_levels', methods=['GET'])
def get_provincial_regulation_levels():
    """管理员获取省级规划级别列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT id, name FROM provincial_regulation_levels ORDER BY id")
            levels = cursor.fetchall()
            return jsonify({
                "code": 0,
                "data": levels
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取省级规划级别数据失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_features', methods=['GET'])
def get_features():
    """管理员获取特色标签列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT id, name FROM book_features ORDER BY id")
            features = cursor.fetchall()
            return jsonify({
                "code": 0,
                "data": features
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取特色数据失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_filter_options', methods=['GET'])
def get_filter_options():
    """管理员获取筛选选项"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取学校层次 - 使用预定义选项确保完整性
            predefined_levels = ['中职', '专科', '本科', '技校']
            cursor.execute("SELECT DISTINCT level as name FROM sample_books WHERE level IS NOT NULL ORDER BY level")
            db_levels = [row["name"] for row in cursor.fetchall()]

            # 合并预定义和数据库中的层次，确保完整性
            all_levels = list(dict.fromkeys(predefined_levels + db_levels))  # 去重并保持顺序
            levels = [{"id": i+1, "name": level} for i, level in enumerate(all_levels)]

            # 获取图书类型
            cursor.execute("SELECT DISTINCT book_type as name FROM sample_books WHERE book_type IS NOT NULL ORDER BY book_type")
            types = [{"id": i+1, "name": row["name"]} for i, row in enumerate(cursor.fetchall())]

            # 获取国家规划级别
            cursor.execute("SELECT id, name FROM national_regulation_levels ORDER BY id")
            national_levels = cursor.fetchall()

            # 获取省级规划级别
            cursor.execute("SELECT id, name FROM provincial_regulation_levels ORDER BY id")
            provincial_levels = cursor.fetchall()

            # 获取出版社列表 - 与左侧出版社列表保持一致
            cursor.execute("""
                SELECT DISTINCT COALESCE(pc.name, u.name) as name
                FROM users u
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                JOIN sample_books sb ON u.user_id = sb.publisher_id
                WHERE u.role = 'publisher'
                ORDER BY COALESCE(pc.name, u.name)
            """)
            publishers = [{"id": i+1, "name": row["name"]} for i, row in enumerate(cursor.fetchall())]

            # 获取特色标签
            cursor.execute("SELECT id, name FROM book_features ORDER BY id")
            features = cursor.fetchall()

            return jsonify({
                "code": 0,
                "data": {
                    "levels": levels,
                    "types": types,
                    "national_levels": national_levels,
                    "provincial_levels": provincial_levels,
                    "publishers": publishers,
                    "features": features
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取筛选选项失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/filter_admin_samples', methods=['GET'])
def filter_admin_samples():
    """管理员高级筛选样书"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    # 获取筛选参数
    search = request.args.get('search', '').strip()
    levels_json = request.args.get('levels', '[]')
    types_json = request.args.get('types', '[]')
    ranks_json = request.args.get('ranks', '[]')
    national_levels_json = request.args.get('national_levels', '[]')
    provincial_levels_json = request.args.get('provincial_levels', '[]')
    publishers_json = request.args.get('publishers', '[]')
    features_json = request.args.get('features', '[]')
    publication_date_filter = request.args.get('publication_date_filter', '')
    publication_start_date = request.args.get('publication_start_date', '')
    publication_end_date = request.args.get('publication_end_date', '')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 12))

    connection = get_db_connection()
    try:
        import json
        levels = json.loads(levels_json) if levels_json else []
        types = json.loads(types_json) if types_json else []
        ranks = json.loads(ranks_json) if ranks_json else []
        national_levels = json.loads(national_levels_json) if national_levels_json else []
        provincial_levels = json.loads(provincial_levels_json) if provincial_levels_json else []
        publishers = json.loads(publishers_json) if publishers_json else []
        features = json.loads(features_json) if features_json else []

        with connection.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            # 搜索条件
            if search:
                where_conditions.append("(sb.name LIKE %s OR sb.author LIKE %s OR sb.isbn LIKE %s)")
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])

            # 学校层次筛选
            if levels:
                placeholders = ','.join(['%s'] * len(levels))
                where_conditions.append(f"sb.level IN ({placeholders})")
                params.extend(levels)

            # 图书类型筛选
            if types:
                placeholders = ','.join(['%s'] * len(types))
                where_conditions.append(f"sb.book_type IN ({placeholders})")
                params.extend(types)

            # 规划级别筛选
            if ranks:
                rank_conditions = []
                for rank in ranks:
                    if rank == "国家规划":
                        rank_conditions.append("sb.national_regulation = 1")
                    elif rank == "省级规划":
                        rank_conditions.append("sb.provincial_regulation = 1")
                    elif rank == "普通教材":
                        rank_conditions.append("(sb.national_regulation = 0 AND sb.provincial_regulation = 0)")
                if rank_conditions:
                    where_conditions.append(f"({' OR '.join(rank_conditions)})")

            # 国家规划级别筛选
            if national_levels:
                placeholders = ','.join(['%s'] * len(national_levels))
                where_conditions.append(f"sb.national_regulation_level_id IN (SELECT id FROM national_regulation_levels WHERE name IN ({placeholders}))")
                params.extend(national_levels)

            # 省级规划级别筛选
            if provincial_levels:
                placeholders = ','.join(['%s'] * len(provincial_levels))
                where_conditions.append(f"sb.provincial_regulation_level_id IN (SELECT id FROM provincial_regulation_levels WHERE name IN ({placeholders}))")
                params.extend(provincial_levels)

            # 出版社筛选 - 通过用户表和公司表进行匹配
            if publishers:
                placeholders = ','.join(['%s'] * len(publishers))
                where_conditions.append(f"""
                    sb.publisher_id IN (
                        SELECT u.user_id 
                        FROM users u 
                        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id 
                        WHERE COALESCE(pc.name, u.name) IN ({placeholders})
                    )
                """)
                params.extend(publishers)

            # 特色标签筛选
            if features:
                placeholders = ','.join(['%s'] * len(features))
                where_conditions.append(f"EXISTS (SELECT 1 FROM sample_book_features sbf JOIN book_features f ON sbf.feature_id = f.id WHERE sbf.sample_id = sb.id AND f.name IN ({placeholders}))")
                params.extend(features)

            # 添加出版日期筛选
            if publication_date_filter:
                if publication_date_filter == 'recent_three_years':
                    # 近三年：从三年前的今天到今天
                    where_conditions.append("sb.publication_date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)")
                elif publication_date_filter == 'custom' and publication_start_date and publication_end_date:
                    # 自定义日期范围
                    where_conditions.append("sb.publication_date >= %s AND sb.publication_date <= %s")
                    params.extend([publication_start_date, publication_end_date])

            # 构建WHERE子句
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # 查询总数
            count_sql = f"""
                SELECT COUNT(DISTINCT sb.id)
                FROM sample_books sb
                {where_clause}
            """
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['COUNT(DISTINCT sb.id)']

            # 查询样书数据
            offset = (page - 1) * limit
            sql = f"""
                SELECT DISTINCT sb.*,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       GROUP_CONCAT(DISTINCT f.name) as feature_name
                FROM sample_books sb
                LEFT JOIN sample_book_features sbf ON sb.id = sbf.sample_id
                LEFT JOIN book_features f ON sbf.feature_id = f.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                {where_clause}
                GROUP BY sb.id
                ORDER BY sb.id DESC
                LIMIT %s OFFSET %s
            """

            cursor.execute(sql, params + [limit, offset])
            samples = cursor.fetchall()

            return jsonify({
                "code": 0,
                "data": {
                    "samples": samples,
                    "total": total,
                    "page": page,
                    "limit": limit
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"筛选样书失败: {str(e)}"})
    finally:
        connection.close()

@admin_bp.route('/get_statistics', methods=['GET'])
def get_statistics():
    """获取管理员仪表盘统计数据"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 1. 用户统计
            cursor.execute("SELECT COUNT(*) as total FROM users")
            total_users = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'teacher'")
            teacher_count = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'publisher'")
            publisher_count = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'dealer'")
            dealer_count = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'admin'")
            admin_count = cursor.fetchone()['count']
            
            user_stats = {
                'total': total_users,
                'teacher': teacher_count,
                'publisher': publisher_count,
                'dealer': dealer_count,
                'admin': admin_count
            }
            
            # 2. 样书统计
            cursor.execute("SELECT COUNT(*) as total FROM sample_books")
            sample_total = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as digital FROM sample_books WHERE material_type = '数字教材'")
            digital_count = cursor.fetchone()['digital'] or 0
            
            cursor.execute("SELECT COUNT(*) as physical FROM sample_books WHERE material_type = '纸质教材'")
            physical_count = cursor.fetchone()['physical'] or 0
            
            cursor.execute("SELECT COUNT(*) as national FROM sample_books WHERE national_regulation = 1")
            national_count = cursor.fetchone()['national'] or 0
            
            cursor.execute("SELECT COUNT(*) as provincial FROM sample_books WHERE provincial_regulation = 1")
            provincial_count = cursor.fetchone()['provincial'] or 0
            
            sample_stats = {
                'total': sample_total,
                'digital': digital_count,
                'physical': physical_count,
                'national': national_count,
                'provincial': provincial_count
            }
            
            # 3. 样书申请统计
            cursor.execute("SELECT COUNT(*) as total FROM sample_requests")
            sample_request_total = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as count FROM sample_requests WHERE status = 'pending'")
            sample_request_pending = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM sample_requests WHERE status = 'approved'")
            sample_request_approved = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM sample_requests WHERE status = 'rejected'")
            sample_request_rejected = cursor.fetchone()['count']
            
            sample_request_stats = {
                'total': sample_request_total,
                'pending': sample_request_pending,
                'approved': sample_request_approved,
                'rejected': sample_request_rejected
            }
            
            # 4. 书展活动统计
            cursor.execute("SELECT COUNT(*) as total FROM book_exhibitions")
            exhibition_total = cursor.fetchone()['total']
            
            cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE status = 'draft'")
            exhibition_draft = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE status = 'published'")
            exhibition_published = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE status = 'cancelled'")
            exhibition_cancelled = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM book_exhibitions WHERE status = 'ended'")
            exhibition_ended = cursor.fetchone()['count']
            
            exhibition_stats = {
                'total': exhibition_total,
                'draft': exhibition_draft,
                'published': exhibition_published,
                'cancelled': exhibition_cancelled,
                'ended': exhibition_ended
            }
            
            # 5. 近期活动统计
            recent_activities = []
            
            # 5.1 最近注册的用户
            cursor.execute("""
                SELECT user_id, username, role, name, created_at
                FROM users 
                ORDER BY created_at DESC 
                LIMIT 3
            """)
            recent_users = cursor.fetchall()
            
            for user in recent_users:
                role_names = {
                    'teacher': '教师',
                    'publisher': '出版社',
                    'dealer': '经销商',
                    'admin': '管理员'
                }
                recent_activities.append({
                    'type': 'user',
                    'message': f'新{role_names.get(user["role"], "")}用户 {user["username"]} 注册',
                    'timestamp': user['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 5.2 最近的样书申请
            cursor.execute("""
                SELECT sr.request_id, sr.teacher_id, sr.textbook_id, sr.status, sr.request_date,
                       u.username as teacher_name, sb.name as sample_name
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                ORDER BY sr.request_date DESC
                LIMIT 3
            """)
            recent_sample_requests = cursor.fetchall()
            
            for request in recent_sample_requests:
                status_text = {
                    'pending': '待处理',
                    'approved': '已通过',
                    'rejected': '已拒绝'
                }
                
                recent_activities.append({
                    'type': 'sample_request',
                    'message': f'教师 {request["teacher_name"]} 申请样书 "{request["sample_name"]}" ({status_text.get(request["status"], "")})',
                    'timestamp': request["request_date"].strftime('%Y-%m-%d %H:%M:%S') if request["request_date"] else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 5.3 最近的书展活动
            cursor.execute("""
                SELECT be.id, be.title, be.status, be.created_at,
                       u.username as initiator_name, s.name as school_name
                FROM book_exhibitions be
                JOIN users u ON be.initiator_id = u.user_id
                JOIN schools s ON be.school_id = s.id
                ORDER BY be.created_at DESC
                LIMIT 3
            """)
            recent_exhibitions = cursor.fetchall()
            
            for exhibition in recent_exhibitions:
                status_text = {
                    'draft': '草稿',
                    'published': '已发布',
                    'cancelled': '已取消',
                    'ended': '已结束'
                }
                
                recent_activities.append({
                    'type': 'exhibition',
                    'message': f'用户 {exhibition["initiator_name"]} 创建书展 "{exhibition["title"]}" ({status_text.get(exhibition["status"], "")})',
                    'timestamp': exhibition["created_at"].strftime('%Y-%m-%d %H:%M:%S') if exhibition["created_at"] else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 5.4 最新的样书
            cursor.execute("""
                SELECT sb.id, sb.name, 
                       u.username as publisher_name
                FROM sample_books sb
                LEFT JOIN users u ON sb.publisher_id = u.user_id
                ORDER BY sb.id DESC
                LIMIT 3
            """)
            recent_books = cursor.fetchall()
            
            for book in recent_books:
                publisher_name = book["publisher_name"] if book["publisher_name"] else "未知出版社"
                
                recent_activities.append({
                    'type': 'book',
                    'message': f'{publisher_name} 添加了新样书 "{book["name"]}"',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 按时间排序最近活动
            recent_activities.sort(key=lambda x: x['timestamp'], reverse=True)
            recent_activities = recent_activities[:5]  # 只取前5个
            
            # 7. 返回所有统计数据
            return jsonify({
                'code': 0,
                'data': {
                    'user_stats': user_stats,
                    'sample_stats': sample_stats,
                    'sample_request_stats': sample_request_stats,
                    'exhibition_stats': exhibition_stats,
                    'recent_activities': recent_activities
                }
            })
    
    except Exception as e:
        return jsonify({
            'code': 1,
            'message': f'获取统计数据失败: {str(e)}'
        })
    finally:
        connection.close()

@admin_bp.route('/add_email_config', methods=['POST'])
def add_email_config():
    """
    添加邮件配置
    请求数据:
        smtp_host: SMTP服务器地址
        smtp_port: SMTP端口
        smtp_username: SMTP用户名
        smtp_password: SMTP密码
        from_email: 发件人邮箱
        from_name: 发件人名称
        use_tls: 是否使用TLS
        use_ssl: 是否使用SSL
        is_active: 是否启用
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        
        # 必填字段验证
        required_fields = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({"code": 1, "message": f"缺少必填字段: {field}"})

        smtp_host = data['smtp_host']
        smtp_port = int(data['smtp_port'])
        smtp_username = data['smtp_username']
        smtp_password = data['smtp_password']
        from_email = data['from_email']
        from_name = data.get('from_name', '')
        use_tls = data.get('use_tls', True)
        use_ssl = data.get('use_ssl', False)
        is_active = data.get('is_active', True)

        # 验证邮箱格式
        import re
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', from_email):
            return jsonify({"code": 1, "message": "发件人邮箱格式不正确"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查邮箱是否已存在
        cursor.execute("SELECT id FROM email_config WHERE from_email = %s", (from_email,))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "该邮箱已存在"})

        # 插入新配置
        insert_query = """
        INSERT INTO email_config (smtp_host, smtp_port, smtp_username, smtp_password, 
                                from_email, from_name, use_tls, use_ssl, is_active, created_at, updated_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """
        
        cursor.execute(insert_query, (
            smtp_host, smtp_port, smtp_username, smtp_password,
            from_email, from_name, use_tls, use_ssl, is_active
        ))

        config_id = cursor.lastrowid
        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "邮件配置添加成功", "config_id": config_id})
    except ValueError:
        return jsonify({"code": 1, "message": "端口必须是数字"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加邮件配置失败: {str(e)}"})

@admin_bp.route('/edit_email_config', methods=['POST'])
def edit_email_config():
    """
    编辑邮件配置
    请求数据:
        id: 配置ID
        smtp_host: SMTP服务器地址
        smtp_port: SMTP端口
        smtp_username: SMTP用户名
        smtp_password: SMTP密码 (可选，为空则不更新)
        from_email: 发件人邮箱
        from_name: 发件人名称
        use_tls: 是否使用TLS
        use_ssl: 是否使用SSL
        is_active: 是否启用
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        
        # 必填字段验证
        if 'id' not in data or not data['id']:
            return jsonify({"code": 1, "message": "缺少配置ID"})

        config_id = data['id']
        required_fields = ['smtp_host', 'smtp_port', 'smtp_username', 'from_email']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({"code": 1, "message": f"缺少必填字段: {field}"})

        smtp_host = data['smtp_host']
        smtp_port = int(data['smtp_port'])
        smtp_username = data['smtp_username']
        smtp_password = data.get('smtp_password', '')  # 可选，为空则不更新
        from_email = data['from_email']
        from_name = data.get('from_name', '')
        use_tls = data.get('use_tls', True)
        use_ssl = data.get('use_ssl', False)
        is_active = data.get('is_active', True)

        # 验证邮箱格式
        import re
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', from_email):
            return jsonify({"code": 1, "message": "发件人邮箱格式不正确"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查配置是否存在
        cursor.execute("SELECT id, from_email FROM email_config WHERE id = %s", (config_id,))
        config = cursor.fetchone()
        if not config:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "配置不存在"})

        # 检查邮箱是否被其他配置使用
        if from_email != config['from_email']:
            cursor.execute("SELECT id FROM email_config WHERE from_email = %s AND id != %s", (from_email, config_id))
            if cursor.fetchone():
                cursor.close()
                connection.close()
                return jsonify({"code": 1, "message": "该邮箱已被其他配置使用"})

        # 更新配置
        if smtp_password:
            # 更新包括密码
            update_query = """
            UPDATE email_config 
            SET smtp_host = %s, smtp_port = %s, smtp_username = %s, smtp_password = %s,
                from_email = %s, from_name = %s, use_tls = %s, use_ssl = %s, 
                is_active = %s, updated_at = NOW()
            WHERE id = %s
            """
            cursor.execute(update_query, (
                smtp_host, smtp_port, smtp_username, smtp_password,
                from_email, from_name, use_tls, use_ssl, is_active, config_id
            ))
        else:
            # 不更新密码
            update_query = """
            UPDATE email_config 
            SET smtp_host = %s, smtp_port = %s, smtp_username = %s,
                from_email = %s, from_name = %s, use_tls = %s, use_ssl = %s, 
                is_active = %s, updated_at = NOW()
            WHERE id = %s
            """
            cursor.execute(update_query, (
                smtp_host, smtp_port, smtp_username,
                from_email, from_name, use_tls, use_ssl, is_active, config_id
            ))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "邮件配置更新成功"})
    except ValueError:
        return jsonify({"code": 1, "message": "端口必须是数字"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新邮件配置失败: {str(e)}"})

@admin_bp.route('/delete_email_config', methods=['POST'])
def delete_email_config():
    """
    删除邮件配置
    请求数据:
        id: 配置ID
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        
        if 'id' not in data or not data['id']:
            return jsonify({"code": 1, "message": "未提供配置ID"})

        config_id = data['id']

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查配置是否存在
        cursor.execute("SELECT id FROM email_config WHERE id = %s", (config_id,))
        if not cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "配置不存在"})

        # 删除配置
        cursor.execute("DELETE FROM email_config WHERE id = %s", (config_id,))
        
        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "邮件配置删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除邮件配置失败: {str(e)}"})

@admin_bp.route('/toggle_email_config_status', methods=['POST'])
def toggle_email_config_status():
    """
    切换邮件配置启用状态
    请求数据:
        id: 配置ID
        is_active: 是否启用
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        
        if 'id' not in data or not data['id']:
            return jsonify({"code": 1, "message": "未提供配置ID"})
        if 'is_active' not in data:
            return jsonify({"code": 1, "message": "未提供状态"})

        config_id = data['id']
        is_active = data['is_active']

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查配置是否存在
        cursor.execute("SELECT id FROM email_config WHERE id = %s", (config_id,))
        if not cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "配置不存在"})

        # 更新状态
        cursor.execute("UPDATE email_config SET is_active = %s, updated_at = NOW() WHERE id = %s", 
                      (is_active, config_id))
        
        connection.commit()
        cursor.close()
        connection.close()

        status_text = "启用" if is_active else "禁用"
        return jsonify({"code": 0, "message": f"邮件配置已{status_text}"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新配置状态失败: {str(e)}"})

@admin_bp.route('/test_email_config', methods=['POST'])
def test_email():
    """
    发送测试邮件
    请求数据:
        config_id: 配置ID
        test_email: 测试邮箱地址
    返回:
        发送结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        
        if 'config_id' not in data or not data['config_id']:
            return jsonify({"code": 1, "message": "缺少配置ID"})
            
        if 'test_email' not in data or not data['test_email']:
            return jsonify({"code": 1, "message": "缺少测试邮箱地址"})

        config_id = data['config_id']
        test_email = data['test_email']

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取配置
        cursor.execute("SELECT * FROM email_config WHERE id = %s", (config_id,))
        config = cursor.fetchone()
        
        cursor.close()
        connection.close()

        if not config:
            return jsonify({"code": 1, "message": "邮件配置不存在"})

        # 使用邮件服务模块进行测试
        from app.services.email_service import EmailService
        from datetime import datetime

        try:
            # 创建临时邮件服务实例，使用指定配置
            email_service = EmailService()

            # 临时替换配置列表，只包含当前测试的配置
            original_configs = email_service.configs
            email_service.configs = [config]

            # 准备测试邮件内容
            subject = '邮件配置测试'
            content = f"""邮件配置测试成功！

配置信息：
- SMTP服务器：{config['smtp_host']}:{config['smtp_port']}
- 发件人：{config['from_name'] or '未设置'} <{config['from_email']}>
- TLS：{'启用' if config.get('use_tls', 1) else '禁用'}
- SSL：{'启用' if config.get('use_ssl', 0) else '禁用'}
- 测试时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

如果您收到此邮件，说明邮件配置正常工作。

此邮件由样书管理系统自动发送，请勿回复。"""

            # 发送测试邮件
            result = email_service.send_email(
                to_emails=[test_email],
                subject=subject,
                content=content,
                email_type='notification'
            )

            # 恢复原始配置
            email_service.configs = original_configs

            if result['success']:
                return jsonify({"code": 0, "message": f"测试邮件已发送到 {test_email}，请检查收件箱"})
            else:
                return jsonify({"code": 1, "message": f"邮件发送失败: {result['message']}"})

        except Exception as e:
            return jsonify({"code": 1, "message": f"邮件发送失败: {str(e)}"})
            
    except Exception as e:
        return jsonify({"code": 1, "message": f"发送测试邮件失败: {str(e)}"})

@admin_bp.route('/test_email_connection', methods=['POST'])
def test_email_config():
    """
    测试邮件配置连接
    请求数据:
        id: 配置ID  
    返回:
        连接测试结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        
        if 'id' not in data or not data['id']:
            return jsonify({"code": 1, "message": "缺少配置ID"})

        config_id = data['id']

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取配置
        cursor.execute("SELECT * FROM email_config WHERE id = %s", (config_id,))
        config = cursor.fetchone()
        
        cursor.close()
        connection.close()

        if not config:
            return jsonify({"code": 1, "message": "邮件配置不存在"})

        # 使用邮件服务模块进行连接测试
        from app.services.email_service import EmailService

        try:
            # 创建临时邮件服务实例，使用指定配置
            email_service = EmailService()

            # 临时替换配置列表，只包含当前测试的配置
            original_configs = email_service.configs
            email_service.configs = [config]

            # 使用邮件服务的连接测试功能
            result = email_service.test_connection()

            # 恢复原始配置
            email_service.configs = original_configs

            if result['success']:
                return jsonify({"code": 0, "message": "SMTP连接测试成功"})
            else:
                return jsonify({"code": 1, "message": result['message']})

        except Exception as e:
            return jsonify({"code": 1, "message": f"连接测试失败: {str(e)}"})
            
    except Exception as e:
        return jsonify({"code": 1, "message": f"测试邮件配置连接失败: {str(e)}"})

@admin_bp.route('/batch_test_email_configs', methods=['POST'])
def batch_test_email_configs():
    """
    批量测试邮件配置连接
    请求数据:
        config_ids: 配置ID列表
    返回:
        测试结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.get_json()
        
        if 'config_ids' not in data or not data['config_ids']:
            return jsonify({"code": 1, "message": "未提供配置ID列表"})

        config_ids = data['config_ids']

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取配置
        placeholders = ','.join(['%s'] * len(config_ids))
        cursor.execute(f"SELECT * FROM email_config WHERE id IN ({placeholders})", config_ids)
        configs = cursor.fetchall()
        
        cursor.close()
        connection.close()

        # 使用邮件服务模块进行批量测试
        from app.services.email_service import EmailService

        results = []

        for config in configs:
            try:
                # 创建临时邮件服务实例，使用指定配置
                email_service = EmailService()

                # 临时替换配置列表，只包含当前测试的配置
                original_configs = email_service.configs
                email_service.configs = [config]

                # 使用邮件服务的连接测试功能
                result = email_service.test_connection()

                # 恢复原始配置
                email_service.configs = original_configs

                results.append({
                    'id': config['id'],
                    'email': config['from_email'],
                    'success': result['success'],
                    'message': '连接成功' if result['success'] else result['message']
                })

            except Exception as e:
                results.append({
                    'id': config['id'],
                    'email': config['from_email'],
                    'success': False,
                    'message': str(e)
                })

        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        return jsonify({
            "code": 0, 
            "message": f"批量测试完成，成功 {success_count}/{total_count}",
            "data": results
        })
            
    except Exception as e:
        return jsonify({"code": 1, "message": f"批量测试失败: {str(e)}"})

@admin_bp.route('/admin_email_configs', methods=['GET'])
def admin_email_configs():
    """
    渲染邮件配置管理页面
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    return render_template('pc_admin_email_configs.html')

@admin_bp.route('/reload_email_service', methods=['POST'])
def reload_email_service():
    """
    重新加载邮件服务配置
    返回:
        操作结果
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        from app.services.email_service import email_service
        
        # 重新加载配置
        success = email_service.reload_config()
        
        if success:
            # 获取重新加载后的配置信息
            config_list = email_service.get_config_list()
            
            return jsonify({
                "code": 0, 
                "message": f"邮件服务配置重新加载成功，当前有效配置: {len(config_list)} 个",
                "data": {
                    "config_count": len(config_list),
                    "configs": config_list
                }
            })
        else:
            return jsonify({"code": 1, "message": "重新加载失败，没有找到有效的邮件配置"})
            
    except Exception as e:
        return jsonify({"code": 1, "message": f"重新加载邮件服务配置失败: {str(e)}"})

@admin_bp.route('/get_email_service_status', methods=['GET'])
def get_email_service_status():
    """
    获取邮件服务状态
    返回:
        邮件服务当前状态信息
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        from app.services.email_service import email_service
        
        # 获取配置信息
        config_list = email_service.get_config_list()
        
        # 测试连接
        connection_test = email_service.test_connection()
        
        return jsonify({
            "code": 0,
            "data": {
                "config_count": len(config_list),
                "configs": config_list,
                "connection_test": connection_test,
                "service_available": len(config_list) > 0
            }
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取邮件服务状态失败: {str(e)}"})

# @admin_bp.route('/manage_reports')
# def manage_reports():
#     """管理员报备管理页面"""
#     if 'user_id' not in session or session.get('role') != 'admin':
#         return redirect(url_for('auth.login'))

#     return render_template('admin_manage_reports.html')

# ==================== 样书申请管理相关API ====================

@admin_bp.route('/get_admin_pending_sample_requests', methods=['GET'])
def get_admin_pending_sample_requests():
    """获取待处理样书申请列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        search = request.args.get('search', '').strip()
        publisher_id = request.args.get('publisher_id', '')
        date_filter = request.args.get('date_filter', 'all')

        # 参数验证
        if page < 1:
            page = 1
        if limit < 1 or limit > 50:
            limit = 10

        offset = (page - 1) * limit

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 构建查询条件
            where_conditions = ["sr.status = 'pending'", "sr.order_number IS NOT NULL"]
            params = []

            # 搜索条件
            if search:
                where_conditions.append("""
                    (sb.name LIKE %s OR u.name LIKE %s OR s.name LIKE %s
                     OR sr.order_number LIKE %s OR pub_user.name LIKE %s OR pc.name LIKE %s)
                """)
                search_param = f'%{search}%'
                params.extend([search_param] * 6)

            # 出版社筛选
            if publisher_id and publisher_id != 'all':
                where_conditions.append("sb.publisher_id = %s")
                params.append(publisher_id)

            # 日期筛选
            if date_filter == 'today':
                where_conditions.append("DATE(sr.request_date) = CURDATE()")
            elif date_filter == 'week':
                where_conditions.append("sr.request_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)")
            elif date_filter == 'month':
                where_conditions.append("sr.request_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)")

            where_clause = " AND ".join(where_conditions)

            # 查询待处理申请订单
            sql = f"""
                SELECT DISTINCT sr.order_number, u.name as teacher_name, u.user_id as teacher_id,
                       s.name as school_name, u.phone_number as teacher_phone,
                       MIN(sr.request_date) as request_date,
                       SUM(sr.quantity) as total_quantity,
                       COUNT(DISTINCT sr.textbook_id) as book_count,
                       MAX(sr.purpose) as purpose,
                       MAX(sr.request_reason) as request_reason,
                       MAX(tc.course_name) as course_name,
                       MAX(tc.semester) as semester,
                       GROUP_CONCAT(DISTINCT pc.name ORDER BY pc.name SEPARATOR ', ') as publisher_names
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE {where_clause}
                GROUP BY sr.order_number, u.user_id, u.name, s.name, u.phone_number
                ORDER BY MIN(sr.request_date) DESC
                LIMIT %s OFFSET %s
            """

            params.extend([limit, offset])
            cursor.execute(sql, params)
            orders = cursor.fetchall()

            # 获取总数
            count_params = params[:-2]  # 移除limit和offset参数
            count_sql = f"""
                SELECT COUNT(DISTINCT sr.order_number) as total
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE {where_clause}
            """

            cursor.execute(count_sql, count_params)
            total = cursor.fetchone()['total']

            # 为每个订单获取详细的样书信息
            for order in orders:
                books_sql = """
                    SELECT sr.request_id, sb.name as book_name, sb.isbn, sb.author, sr.quantity,
                           tc.course_name, tc.semester, sr.request_reason as book_remarks,
                           pc.name as publisher_name
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                    LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                    WHERE sr.order_number = %s AND sr.status = 'pending'
                    ORDER BY sr.request_id
                """
                cursor.execute(books_sql, (order['order_number'],))
                order['books'] = cursor.fetchall()

                # 格式化时间
                if order['request_date']:
                    order['request_date'] = order['request_date'].strftime('%Y-%m-%d %H:%M')

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": orders,
                "count": total
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取待处理申请失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/get_admin_processed_sample_requests', methods=['GET'])
def get_admin_processed_sample_requests():
    """获取已处理样书申请列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        search = request.args.get('search', '').strip()
        publisher_id = request.args.get('publisher_id', '')
        status_filter = request.args.get('status', 'all')  # all, approved, rejected, shipped
        date_filter = request.args.get('date_filter', 'all')

        # 参数验证
        if page < 1:
            page = 1
        if limit < 1 or limit > 50:
            limit = 10

        offset = (page - 1) * limit

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 构建查询条件
            where_conditions = ["sr.status IN ('approved', 'rejected')", "sr.order_number IS NOT NULL"]
            params = []

            # 状态筛选
            if status_filter == 'approved':
                where_conditions = ["sr.status = 'approved'", "sr.order_number IS NOT NULL"]
            elif status_filter == 'rejected':
                where_conditions = ["sr.status = 'rejected'", "sr.order_number IS NOT NULL"]
            elif status_filter == 'shipped':
                where_conditions = ["sr.status = 'approved'", "sr.shipping_date IS NOT NULL", "sr.order_number IS NOT NULL"]
            elif status_filter == 'approved_not_shipped':
                where_conditions = ["sr.status = 'approved'", "sr.shipping_date IS NULL", "sr.order_number IS NOT NULL"]

            # 搜索条件
            if search:
                where_conditions.append("""
                    (sb.name LIKE %s OR u.name LIKE %s OR s.name LIKE %s
                     OR sr.order_number LIKE %s OR pub_user.name LIKE %s OR pc.name LIKE %s)
                """)
                search_param = f'%{search}%'
                params.extend([search_param] * 6)

            # 出版社筛选
            if publisher_id and publisher_id != 'all':
                where_conditions.append("sb.publisher_id = %s")
                params.append(publisher_id)

            # 日期筛选
            if date_filter == 'today':
                where_conditions.append("DATE(sr.approval_date) = CURDATE()")
            elif date_filter == 'week':
                where_conditions.append("sr.approval_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)")
            elif date_filter == 'month':
                where_conditions.append("sr.approval_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)")

            where_clause = " AND ".join(where_conditions)

            # 查询已处理申请订单
            sql = f"""
                SELECT DISTINCT sr.order_number, u.name as teacher_name, u.user_id as teacher_id,
                       s.name as school_name, u.phone_number as teacher_phone,
                       MIN(sr.request_date) as request_date,
                       MAX(sr.approval_date) as approval_date,
                       MAX(sr.shipping_date) as shipping_date,
                       MAX(sr.status) as status,
                       MAX(sr.tracking_number) as tracking_number,
                       MAX(sr.shipping_company) as shipping_company,
                       SUM(sr.quantity) as total_quantity,
                       COUNT(DISTINCT sr.textbook_id) as book_count,
                       MAX(sr.purpose) as purpose,
                       MAX(sr.request_reason) as request_reason,
                       MAX(sr.reject_reason) as reject_reason,
                       MAX(tc.course_name) as course_name,
                       MAX(tc.semester) as semester,
                       GROUP_CONCAT(DISTINCT pc.name ORDER BY pc.name SEPARATOR ', ') as publisher_names
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE {where_clause}
                GROUP BY sr.order_number, u.user_id, u.name, s.name, u.phone_number
                ORDER BY MAX(sr.approval_date) DESC
                LIMIT %s OFFSET %s
            """

            params.extend([limit, offset])
            cursor.execute(sql, params)
            orders = cursor.fetchall()

            # 获取总数
            count_params = params[:-2]  # 移除limit和offset参数
            count_sql = f"""
                SELECT COUNT(DISTINCT sr.order_number) as total
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE {where_clause}
            """

            cursor.execute(count_sql, count_params)
            total = cursor.fetchone()['total']

            # 为每个订单获取详细的样书信息
            for order in orders:
                books_sql = """
                    SELECT sr.request_id, sb.name as book_name, sb.isbn, sb.author, sr.quantity,
                           tc.course_name, tc.semester, sr.request_reason as book_remarks,
                           pc.name as publisher_name, sr.status
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                    LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                    LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                    WHERE sr.order_number = %s AND sr.status IN ('approved', 'rejected')
                    ORDER BY sr.request_id
                """
                cursor.execute(books_sql, (order['order_number'],))
                order['books'] = cursor.fetchall()

                # 格式化时间
                if order['request_date']:
                    order['request_date'] = order['request_date'].strftime('%Y-%m-%d %H:%M')
                if order['approval_date']:
                    order['approval_date'] = order['approval_date'].strftime('%Y-%m-%d %H:%M')
                if order['shipping_date']:
                    order['shipping_date'] = order['shipping_date'].strftime('%Y-%m-%d %H:%M')

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": orders,
                "count": total
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取已处理申请失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/get_admin_sample_request_detail', methods=['GET'])
def get_admin_sample_request_detail():
    """获取样书申请详情"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        order_number = request.args.get('order_number')
        if not order_number:
            return jsonify({"code": 1, "message": "订单编号不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取订单基本信息
            base_sql = """
                SELECT sr.order_number, sr.status, sr.request_date, sr.approval_date, sr.shipping_date,
                       sr.tracking_number, sr.shipping_company, sr.purpose, sr.reject_reason, sr.request_reason,
                       u.name as teacher_name, u.phone_number as teacher_phone, u.email as teacher_email,
                       s.name as school_name,
                       sa.name as recipient_name, sa.phone_number as recipient_phone,
                       sa.province, sa.city, sa.district, sa.detailed_address,
                       tc.course_name, tc.semester
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                LEFT JOIN shipping_addresses sa ON sr.address_id = sa.address_id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE sr.order_number = %s
                LIMIT 1
            """

            cursor.execute(base_sql, (order_number,))
            order_info = cursor.fetchone()

            if not order_info:
                return jsonify({"code": 1, "message": "订单不存在"})

            # 获取订单中的所有样书
            books_sql = """
                SELECT sr.request_id, sb.id as book_id, sb.name as book_name, sb.author, sb.isbn, sb.price,
                       sr.quantity, tc.course_name, tc.semester, sr.request_reason as book_remarks,
                       pc.name as publisher_name, pub_user.name as publisher_user_name,
                       sr.status as book_status
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE sr.order_number = %s
                ORDER BY sr.request_id
            """

            cursor.execute(books_sql, (order_number,))
            books = cursor.fetchall()

            # 计算总数量
            total_quantity = sum(book['quantity'] for book in books)
            order_info['total_quantity'] = total_quantity

            # 格式化时间
            if order_info['request_date']:
                order_info['request_date'] = order_info['request_date'].strftime('%Y-%m-%d %H:%M')
            if order_info['approval_date']:
                order_info['approval_date'] = order_info['approval_date'].strftime('%Y-%m-%d %H:%M')
            if order_info['shipping_date']:
                order_info['shipping_date'] = order_info['shipping_date'].strftime('%Y-%m-%d %H:%M')

            order_info['books'] = books

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": order_info
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取申请详情失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/admin_approve_sample_request', methods=['POST'])
def admin_approve_sample_request():
    """管理员批准样书申请"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        order_number = data.get('order_number')

        if not order_number:
            return jsonify({"code": 1, "message": "订单编号不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 验证订单是否存在且为待处理状态
            check_sql = """
                SELECT COUNT(*) as count
                FROM sample_requests sr
                WHERE sr.order_number = %s AND sr.status = 'pending'
            """

            cursor.execute(check_sql, (order_number,))
            result = cursor.fetchone()

            if not result or result['count'] == 0:
                return jsonify({"code": 1, "message": "未找到待处理的申请或申请已处理"})

            # 更新申请状态为已批准
            update_sql = """
                UPDATE sample_requests
                SET status = 'approved', approval_date = NOW()
                WHERE order_number = %s AND status = 'pending'
            """

            cursor.execute(update_sql, (order_number,))
            affected_rows = cursor.rowcount

            if affected_rows > 0:
                # 获取批准的申请ID列表用于发送通知
                request_ids_sql = """
                    SELECT request_id
                    FROM sample_requests
                    WHERE order_number = %s AND status = 'approved'
                """
                cursor.execute(request_ids_sql, (order_number,))
                results = cursor.fetchall()
                approved_request_ids = [row['request_id'] for row in results]

                connection.commit()

                # 发送邮件通知
                try:
                    notification_service = SampleNotificationService()
                    notification_service.notify_approval(approved_request_ids)
                except Exception as e:
                    print(f"发送邮件通知失败: {str(e)}")

                # 记录批准样书申请日志
                AuditLogService.log_action(
                    action_type=AuditLogService.ActionType.SAMPLE_APPROVE,
                    description=f"管理员批准样书申请，订单号：{order_number}",
                    target_type='sample_request',
                    target_id=order_number,
                    details={
                        'order_number': order_number,
                        'approved_count': affected_rows,
                        'approved_request_ids': approved_request_ids[:10]  # 只记录前10个ID
                    }
                )

                return jsonify({"code": 0, "message": f"成功批准 {affected_rows} 个申请"})
            else:
                return jsonify({"code": 1, "message": "没有申请被批准"})

    except Exception as e:
        # 记录批准失败日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.SAMPLE_APPROVE,
            result=AuditLogService.Result.FAILURE,
            description="管理员批准样书申请失败",
            target_type='sample_request',
            target_id=data.get('order_number') if 'data' in locals() else None,
            details={
                'error_reason': str(e),
                'order_number': data.get('order_number') if 'data' in locals() else None
            }
        )
        return jsonify({"code": 1, "message": f"批准申请失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/admin_reject_sample_request', methods=['POST'])
def admin_reject_sample_request():
    """管理员拒绝样书申请"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        order_number = data.get('order_number')
        reject_reason = data.get('reject_reason', '').strip()

        if not order_number:
            return jsonify({"code": 1, "message": "订单编号不能为空"})

        if not reject_reason:
            return jsonify({"code": 1, "message": "拒绝原因不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 验证订单是否存在且为待处理状态
            check_sql = """
                SELECT COUNT(*) as count
                FROM sample_requests sr
                WHERE sr.order_number = %s AND sr.status = 'pending'
            """

            cursor.execute(check_sql, (order_number,))
            result = cursor.fetchone()

            if not result or result['count'] == 0:
                return jsonify({"code": 1, "message": "未找到待处理的申请或申请已处理"})

            # 更新申请状态为已拒绝
            update_sql = """
                UPDATE sample_requests
                SET status = 'rejected', approval_date = NOW(), reject_reason = %s
                WHERE order_number = %s AND status = 'pending'
            """

            cursor.execute(update_sql, (reject_reason, order_number))
            affected_rows = cursor.rowcount

            if affected_rows > 0:
                # 获取拒绝的申请ID列表用于发送通知
                request_ids_sql = """
                    SELECT request_id
                    FROM sample_requests
                    WHERE order_number = %s AND status = 'rejected'
                """
                cursor.execute(request_ids_sql, (order_number,))
                results = cursor.fetchall()
                rejected_request_ids = [row['request_id'] for row in results]

                connection.commit()

                # 发送邮件通知
                try:
                    notification_service = SampleNotificationService()
                    notification_service.notify_rejection(rejected_request_ids)
                except Exception as e:
                    print(f"发送邮件通知失败: {str(e)}")

                # 记录拒绝样书申请日志
                AuditLogService.log_action(
                    action_type=AuditLogService.ActionType.SAMPLE_REJECT,
                    description=f"管理员拒绝样书申请，订单号：{order_number}",
                    target_type='sample_request',
                    target_id=order_number,
                    details={
                        'order_number': order_number,
                        'reject_reason': reject_reason,
                        'rejected_count': affected_rows,
                        'rejected_request_ids': rejected_request_ids[:10]  # 只记录前10个ID
                    }
                )

                return jsonify({"code": 0, "message": f"成功拒绝 {affected_rows} 个申请"})
            else:
                return jsonify({"code": 1, "message": "没有申请被拒绝"})

    except Exception as e:
        # 记录拒绝失败日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.SAMPLE_REJECT,
            result=AuditLogService.Result.FAILURE,
            description="管理员拒绝样书申请失败",
            target_type='sample_request',
            target_id=data.get('order_number') if 'data' in locals() else None,
            details={
                'error_reason': str(e),
                'order_number': data.get('order_number') if 'data' in locals() else None,
                'reject_reason': data.get('reject_reason') if 'data' in locals() else None
            }
        )
        return jsonify({"code": 1, "message": f"拒绝申请失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/admin_update_sample_shipping', methods=['POST'])
def admin_update_sample_shipping():
    """管理员更新样书申请物流信息"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        order_number = data.get('order_number')
        tracking_number = data.get('tracking_number', '').strip()
        shipping_company = data.get('shipping_company', '').strip()

        if not order_number:
            return jsonify({"code": 1, "message": "订单编号不能为空"})

        if not tracking_number or not shipping_company:
            return jsonify({"code": 1, "message": "快递单号和快递公司不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 验证订单是否存在且为已批准状态
            check_sql = """
                SELECT COUNT(*) as count
                FROM sample_requests sr
                WHERE sr.order_number = %s AND sr.status = 'approved'
            """

            cursor.execute(check_sql, (order_number,))
            result = cursor.fetchone()

            if not result or result['count'] == 0:
                return jsonify({"code": 1, "message": "未找到已批准的申请或申请未被批准"})

            # 更新物流信息
            update_sql = """
                UPDATE sample_requests
                SET tracking_number = %s, shipping_company = %s, shipping_date = NOW()
                WHERE order_number = %s AND status = 'approved'
            """

            cursor.execute(update_sql, (tracking_number, shipping_company, order_number))
            affected_rows = cursor.rowcount

            if affected_rows > 0:
                # 获取更新物流信息的申请ID列表用于发送通知
                request_ids_sql = """
                    SELECT request_id
                    FROM sample_requests
                    WHERE order_number = %s AND tracking_number = %s
                """
                cursor.execute(request_ids_sql, (order_number, tracking_number))
                results = cursor.fetchall()
                updated_request_ids = [row['request_id'] for row in results]

                connection.commit()

                # 发送邮件通知
                try:
                    notification_service = SampleNotificationService()
                    notification_service.notify_shipping(updated_request_ids)
                except Exception as e:
                    print(f"发送邮件通知失败: {str(e)}")

                return jsonify({"code": 0, "message": f"成功更新 {affected_rows} 个申请的物流信息"})
            else:
                return jsonify({"code": 1, "message": "没有申请的物流信息被更新"})

    except Exception as e:
        return jsonify({"code": 1, "message": f"更新物流信息失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/get_admin_publishers_list', methods=['GET'])
def get_admin_publishers_list():
    """获取出版社列表用于筛选"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取有样书申请的出版社列表
            sql = """
                SELECT DISTINCT u.user_id, u.name as publisher_name, pc.name as company_name
                FROM users u
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                JOIN sample_books sb ON u.user_id = sb.publisher_id
                JOIN sample_requests sr ON sb.id = sr.textbook_id
                WHERE u.role = 'publisher'
                ORDER BY COALESCE(pc.name, u.name)
            """

            cursor.execute(sql)
            publishers = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": publishers
            })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/admin_export_sample_requests', methods=['GET'])
def admin_export_sample_requests():
    """管理员导出样书申请数据"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        # 获取筛选参数
        status_filter = request.args.get('status', 'all')  # all, pending, approved, rejected
        publisher_id = request.args.get('publisher_id', '')
        search = request.args.get('search', '').strip()
        date_filter = request.args.get('date_filter', 'all')

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            # 状态筛选
            if status_filter == 'pending':
                where_conditions.append("sr.status = 'pending'")
            elif status_filter == 'approved':
                where_conditions.append("sr.status = 'approved'")
            elif status_filter == 'rejected':
                where_conditions.append("sr.status = 'rejected'")

            # 搜索条件
            if search:
                where_conditions.append("""
                    (sb.name LIKE %s OR u.name LIKE %s OR s.name LIKE %s
                     OR sr.order_number LIKE %s OR pub_user.name LIKE %s OR pc.name LIKE %s)
                """)
                search_param = f'%{search}%'
                params.extend([search_param] * 6)

            # 出版社筛选
            if publisher_id and publisher_id != 'all':
                where_conditions.append("sb.publisher_id = %s")
                params.append(publisher_id)

            # 日期筛选
            if date_filter == 'today':
                where_conditions.append("DATE(sr.request_date) = CURDATE()")
            elif date_filter == 'week':
                where_conditions.append("sr.request_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)")
            elif date_filter == 'month':
                where_conditions.append("sr.request_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)")

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            # 查询详细数据
            sql = f"""
                SELECT sr.order_number, sr.request_id, sr.status, sr.request_date, sr.approval_date, sr.shipping_date,
                       sr.tracking_number, sr.shipping_company, sr.quantity, sr.purpose, sr.request_reason, sr.reject_reason,
                       u.name as teacher_name, u.phone_number as teacher_phone, u.email as teacher_email,
                       s.name as school_name,
                       sb.name as book_name, sb.author, sb.isbn, sb.price,
                       pc.name as publisher_company, pub_user.name as publisher_user,
                       tc.course_name, tc.semester,
                       sa.name as recipient_name, sa.phone_number as recipient_phone,
                       sa.province, sa.city, sa.district, sa.detailed_address
                FROM sample_requests sr
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                LEFT JOIN publisher_companies pc ON pub_user.publisher_company_id = pc.id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                LEFT JOIN shipping_addresses sa ON sr.address_id = sa.address_id
                WHERE {where_clause}
                ORDER BY sr.request_date DESC
            """

            cursor.execute(sql, params)
            requests_data = cursor.fetchall()

            # 创建Excel工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "样书申请记录"

            # 设置表头
            headers = [
                '订单编号', '申请ID', '状态', '申请时间', '处理时间', '发货时间',
                '快递单号', '快递公司', '申请数量', '用途', '申请理由', '拒绝原因',
                '教师姓名', '教师电话', '教师邮箱', '学校名称',
                '样书名称', '作者', 'ISBN', '价格',
                '出版社公司', '出版社用户', '课程名称', '学期',
                '收件人', '收件电话', '省份', '城市', '区县', '详细地址'
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # 写入数据
            for row, data in enumerate(requests_data, 2):
                ws.cell(row=row, column=1, value=data['order_number'])
                ws.cell(row=row, column=2, value=data['request_id'])
                ws.cell(row=row, column=3, value=data['status'])
                ws.cell(row=row, column=4, value=data['request_date'].strftime('%Y-%m-%d %H:%M') if data['request_date'] else '')
                ws.cell(row=row, column=5, value=data['approval_date'].strftime('%Y-%m-%d %H:%M') if data['approval_date'] else '')
                ws.cell(row=row, column=6, value=data['shipping_date'].strftime('%Y-%m-%d %H:%M') if data['shipping_date'] else '')
                ws.cell(row=row, column=7, value=data['tracking_number'] or '')
                ws.cell(row=row, column=8, value=data['shipping_company'] or '')
                ws.cell(row=row, column=9, value=data['quantity'])
                ws.cell(row=row, column=10, value=data['purpose'] or '')
                ws.cell(row=row, column=11, value=data['request_reason'] or '')
                ws.cell(row=row, column=12, value=data['reject_reason'] or '')
                ws.cell(row=row, column=13, value=data['teacher_name'])
                ws.cell(row=row, column=14, value=data['teacher_phone'])
                ws.cell(row=row, column=15, value=data['teacher_email'] or '')
                ws.cell(row=row, column=16, value=data['school_name'] or '')
                ws.cell(row=row, column=17, value=data['book_name'])
                ws.cell(row=row, column=18, value=data['author'] or '')
                ws.cell(row=row, column=19, value=data['isbn'] or '')
                ws.cell(row=row, column=20, value=float(data['price']) if data['price'] else 0)
                ws.cell(row=row, column=21, value=data['publisher_company'] or '')
                ws.cell(row=row, column=22, value=data['publisher_user'] or '')
                ws.cell(row=row, column=23, value=data['course_name'] or '')
                ws.cell(row=row, column=24, value=data['semester'] or '')
                ws.cell(row=row, column=25, value=data['recipient_name'] or '')
                ws.cell(row=row, column=26, value=data['recipient_phone'] or '')
                ws.cell(row=row, column=27, value=data['province'] or '')
                ws.cell(row=row, column=28, value=data['city'] or '')
                ws.cell(row=row, column=29, value=data['district'] or '')
                ws.cell(row=row, column=30, value=data['detailed_address'] or '')

            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 保存到内存
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'样书申请记录_{timestamp}.xlsx'

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )

    except Exception as e:
        return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

# ==================== 样书字段管理相关 ====================

# 层次管理API
@admin_bp.route('/book_levels', methods=['GET'])
def get_book_levels():
    """获取层次列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM book_levels ORDER BY created_at DESC")
            levels = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": levels
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取层次列表失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_levels', methods=['POST'])
def add_book_level():
    """添加层次"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "层次名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否已存在
            cursor.execute("SELECT id FROM book_levels WHERE name = %s", (name,))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该层次已存在"})

            # 插入新记录
            cursor.execute(
                "INSERT INTO book_levels (name, description) VALUES (%s, %s)",
                (name, description if description else None)
            )
            connection.commit()

            return jsonify({"code": 0, "message": "添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加层次失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_levels/<int:level_id>', methods=['PUT'])
def update_book_level(level_id):
    """更新层次"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "层次名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取旧的层次名称
            cursor.execute("SELECT name FROM book_levels WHERE id = %s", (level_id,))
            old_record = cursor.fetchone()
            if not old_record:
                return jsonify({"code": 1, "message": "层次不存在"})

            old_name = old_record['name']

            # 检查是否存在同名记录（排除当前记录）
            cursor.execute("SELECT id FROM book_levels WHERE name = %s AND id != %s", (name, level_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该层次名称已存在"})

            # 更新层次表记录
            cursor.execute(
                "UPDATE book_levels SET name = %s, description = %s WHERE id = %s",
                (name, description if description else None, level_id)
            )

            # 更新样书表中使用该层次的记录（因为level字段是varchar，直接存储文本）
            if old_name != name:
                cursor.execute(
                    "UPDATE sample_books SET level = %s WHERE level = %s",
                    (name, old_name)
                )
                updated_books = cursor.rowcount
                print(f"更新了 {updated_books} 本样书的层次信息")

            connection.commit()
            return jsonify({"code": 0, "message": "更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新层次失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_levels/<int:level_id>', methods=['DELETE'])
def delete_book_level(level_id):
    """删除层次"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取层次名称
            cursor.execute("SELECT name FROM book_levels WHERE id = %s", (level_id,))
            level_record = cursor.fetchone()
            if not level_record:
                return jsonify({"code": 1, "message": "层次不存在"})

            level_name = level_record['name']

            # 检查是否有样书使用该层次（直接检查文本字段）
            cursor.execute("SELECT COUNT(*) as count FROM sample_books WHERE level = %s", (level_name,))
            result = cursor.fetchone()
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": f"该层次正在被 {result['count']} 本样书使用，无法删除"})

            # 删除记录
            cursor.execute("DELETE FROM book_levels WHERE id = %s", (level_id,))

            connection.commit()
            return jsonify({"code": 0, "message": "删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除层次失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

# 色系管理API
@admin_bp.route('/color_systems', methods=['GET'])
def get_color_systems():
    """获取色系列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM color_systems ORDER BY created_at DESC")
            colors = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": colors
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取色系列表失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/color_systems', methods=['POST'])
def add_color_system():
    """添加色系"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "色系名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否已存在
            cursor.execute("SELECT id FROM color_systems WHERE name = %s", (name,))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该色系已存在"})

            # 插入新记录
            cursor.execute(
                "INSERT INTO color_systems (name, description) VALUES (%s, %s)",
                (name, description if description else None)
            )
            connection.commit()

            return jsonify({"code": 0, "message": "添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加色系失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/color_systems/<int:color_id>', methods=['PUT'])
def update_color_system(color_id):
    """更新色系"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "色系名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取旧的色系名称
            cursor.execute("SELECT name FROM color_systems WHERE id = %s", (color_id,))
            old_record = cursor.fetchone()
            if not old_record:
                return jsonify({"code": 1, "message": "色系不存在"})

            old_name = old_record['name']

            # 检查是否存在同名记录（排除当前记录）
            cursor.execute("SELECT id FROM color_systems WHERE name = %s AND id != %s", (name, color_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该色系名称已存在"})

            # 更新色系表记录
            cursor.execute(
                "UPDATE color_systems SET name = %s, description = %s WHERE id = %s",
                (name, description if description else None, color_id)
            )

            # 更新样书表中使用该色系的记录（因为color_system字段是varchar，直接存储文本）
            if old_name != name:
                cursor.execute(
                    "UPDATE sample_books SET color_system = %s WHERE color_system = %s",
                    (name, old_name)
                )
                updated_books = cursor.rowcount
                print(f"更新了 {updated_books} 本样书的色系信息")

            connection.commit()
            return jsonify({"code": 0, "message": "更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新色系失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/color_systems/<int:color_id>', methods=['DELETE'])
def delete_color_system(color_id):
    """删除色系"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取色系名称
            cursor.execute("SELECT name FROM color_systems WHERE id = %s", (color_id,))
            color_record = cursor.fetchone()
            if not color_record:
                return jsonify({"code": 1, "message": "色系不存在"})

            color_name = color_record['name']

            # 检查是否有样书使用该色系（直接检查文本字段）
            cursor.execute("SELECT COUNT(*) as count FROM sample_books WHERE color_system = %s", (color_name,))
            result = cursor.fetchone()
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": f"该色系正在被 {result['count']} 本样书使用，无法删除"})

            # 删除记录
            cursor.execute("DELETE FROM color_systems WHERE id = %s", (color_id,))

            connection.commit()
            return jsonify({"code": 0, "message": "删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除色系失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

# 教材类型管理API
@admin_bp.route('/material_types', methods=['GET'])
def get_material_types():
    """获取教材类型列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM material_types ORDER BY created_at DESC")
            types = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": types
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取教材类型列表失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/material_types', methods=['POST'])
def add_material_type():
    """添加教材类型"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "教材类型名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否已存在
            cursor.execute("SELECT id FROM material_types WHERE name = %s", (name,))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该教材类型已存在"})

            # 插入新记录
            cursor.execute(
                "INSERT INTO material_types (name, description) VALUES (%s, %s)",
                (name, description if description else None)
            )
            connection.commit()

            return jsonify({"code": 0, "message": "添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加教材类型失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/material_types/<int:type_id>', methods=['PUT'])
def update_material_type(type_id):
    """更新教材类型"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "教材类型名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取旧的教材类型名称
            cursor.execute("SELECT name FROM material_types WHERE id = %s", (type_id,))
            old_record = cursor.fetchone()
            if not old_record:
                return jsonify({"code": 1, "message": "教材类型不存在"})

            old_name = old_record['name']

            # 检查是否存在同名记录（排除当前记录）
            cursor.execute("SELECT id FROM material_types WHERE name = %s AND id != %s", (name, type_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该教材类型名称已存在"})

            # 更新教材类型表记录
            cursor.execute(
                "UPDATE material_types SET name = %s, description = %s WHERE id = %s",
                (name, description if description else None, type_id)
            )

            # 更新样书表中使用该教材类型的记录（因为material_type字段是varchar，直接存储文本）
            if old_name != name:
                cursor.execute(
                    "UPDATE sample_books SET material_type = %s WHERE material_type = %s",
                    (name, old_name)
                )
                updated_books = cursor.rowcount
                print(f"更新了 {updated_books} 本样书的教材类型信息")

            connection.commit()
            return jsonify({"code": 0, "message": "更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新教材类型失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/material_types/<int:type_id>', methods=['DELETE'])
def delete_material_type(type_id):
    """删除教材类型"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取教材类型名称
            cursor.execute("SELECT name FROM material_types WHERE id = %s", (type_id,))
            type_record = cursor.fetchone()
            if not type_record:
                return jsonify({"code": 1, "message": "教材类型不存在"})

            type_name = type_record['name']

            # 检查是否有样书使用该教材类型（直接检查文本字段）
            cursor.execute("SELECT COUNT(*) as count FROM sample_books WHERE material_type = %s", (type_name,))
            result = cursor.fetchone()
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": f"该教材类型正在被 {result['count']} 本样书使用，无法删除"})

            # 删除记录
            cursor.execute("DELETE FROM material_types WHERE id = %s", (type_id,))

            connection.commit()
            return jsonify({"code": 0, "message": "删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除教材类型失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

# 样书特色管理API (使用现有的book_features表)
@admin_bp.route('/book_features', methods=['GET'])
def get_book_features():
    """获取样书特色列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM book_features ORDER BY created_at DESC")
            features = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": features
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书特色列表失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_features', methods=['POST'])
def add_book_feature():
    """添加样书特色"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "特色名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否已存在
            cursor.execute("SELECT id FROM book_features WHERE name = %s", (name,))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该特色已存在"})

            # 插入新记录
            cursor.execute(
                "INSERT INTO book_features (name, description) VALUES (%s, %s)",
                (name, description if description else None)
            )
            connection.commit()

            return jsonify({"code": 0, "message": "添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加样书特色失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_features/<int:feature_id>', methods=['PUT'])
def update_book_feature(feature_id):
    """更新样书特色"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "特色名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否存在同名记录（排除当前记录）
            cursor.execute("SELECT id FROM book_features WHERE name = %s AND id != %s", (name, feature_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该特色名称已存在"})

            # 更新记录
            cursor.execute(
                "UPDATE book_features SET name = %s, description = %s WHERE id = %s",
                (name, description if description else None, feature_id)
            )

            if cursor.rowcount == 0:
                return jsonify({"code": 1, "message": "特色不存在"})

            connection.commit()
            return jsonify({"code": 0, "message": "更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新样书特色失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_features/<int:feature_id>', methods=['DELETE'])
def delete_book_feature(feature_id):
    """删除样书特色"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否有样书使用该特色
            cursor.execute("SELECT COUNT(*) as count FROM sample_book_features WHERE feature_id = %s", (feature_id,))
            result = cursor.fetchone()
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": f"该特色正在被 {result['count']} 本样书使用，无法删除"})

            # 删除记录
            cursor.execute("DELETE FROM book_features WHERE id = %s", (feature_id,))

            if cursor.rowcount == 0:
                return jsonify({"code": 1, "message": "特色不存在"})

            connection.commit()
            return jsonify({"code": 0, "message": "删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除样书特色失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

# book_types管理API (使用现有的book_types表)
@admin_bp.route('/book_types', methods=['GET'])
def get_book_types_new():
    """获取样书类型列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM book_types ORDER BY created_at DESC")
            types = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": types
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书类型列表失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_types', methods=['POST'])
def add_book_type():
    """添加样书类型"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "类型名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否已存在
            cursor.execute("SELECT id FROM book_types WHERE name = %s", (name,))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该类型已存在"})

            # 插入新记录
            cursor.execute(
                "INSERT INTO book_types (name, description) VALUES (%s, %s)",
                (name, description if description else None)
            )
            connection.commit()

            return jsonify({"code": 0, "message": "添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加样书类型失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_types/<int:type_id>', methods=['PUT'])
def update_book_type(type_id):
    """更新样书类型"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "类型名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取旧的类型名称
            cursor.execute("SELECT name FROM book_types WHERE id = %s", (type_id,))
            old_record = cursor.fetchone()
            if not old_record:
                return jsonify({"code": 1, "message": "类型不存在"})

            old_name = old_record['name']

            # 检查是否存在同名记录（排除当前记录）
            cursor.execute("SELECT id FROM book_types WHERE name = %s AND id != %s", (name, type_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该类型名称已存在"})

            # 更新类型表记录
            cursor.execute(
                "UPDATE book_types SET name = %s, description = %s WHERE id = %s",
                (name, description if description else None, type_id)
            )

            # 更新样书表中使用该类型的记录（因为book_type字段是varchar，直接存储文本）
            if old_name != name:
                cursor.execute(
                    "UPDATE sample_books SET book_type = %s WHERE book_type = %s",
                    (name, old_name)
                )
                updated_books = cursor.rowcount
                print(f"更新了 {updated_books} 本样书的类型信息")

            connection.commit()
            return jsonify({"code": 0, "message": "更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新样书类型失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/book_types/<int:type_id>', methods=['DELETE'])
def delete_book_type(type_id):
    """删除样书类型"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 获取类型名称
            cursor.execute("SELECT name FROM book_types WHERE id = %s", (type_id,))
            type_record = cursor.fetchone()
            if not type_record:
                return jsonify({"code": 1, "message": "类型不存在"})

            type_name = type_record['name']

            # 检查是否有样书使用该类型（直接检查文本字段）
            cursor.execute("SELECT COUNT(*) as count FROM sample_books WHERE book_type = %s", (type_name,))
            result = cursor.fetchone()
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": f"该类型正在被 {result['count']} 本样书使用，无法删除"})

            # 删除记录
            cursor.execute("DELETE FROM book_types WHERE id = %s", (type_id,))

            connection.commit()
            return jsonify({"code": 0, "message": "删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除样书类型失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

# 国家规划级别管理API
@admin_bp.route('/national_regulation_levels', methods=['GET'])
def get_national_regulation_levels_new():
    """获取国家规划级别列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM national_regulation_levels ORDER BY created_at DESC")
            levels = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": levels
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取国家规划级别列表失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/national_regulation_levels', methods=['POST'])
def add_national_regulation_level():
    """添加国家规划级别"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "规划级别名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否已存在
            cursor.execute("SELECT id FROM national_regulation_levels WHERE name = %s", (name,))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该规划级别已存在"})

            # 插入新记录
            cursor.execute(
                "INSERT INTO national_regulation_levels (name, description) VALUES (%s, %s)",
                (name, description if description else None)
            )
            connection.commit()

            return jsonify({"code": 0, "message": "添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加国家规划级别失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/national_regulation_levels/<int:level_id>', methods=['PUT'])
def update_national_regulation_level(level_id):
    """更新国家规划级别"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "规划级别名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否存在同名记录（排除当前记录）
            cursor.execute("SELECT id FROM national_regulation_levels WHERE name = %s AND id != %s", (name, level_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该规划级别名称已存在"})

            # 更新记录
            cursor.execute(
                "UPDATE national_regulation_levels SET name = %s, description = %s WHERE id = %s",
                (name, description if description else None, level_id)
            )

            if cursor.rowcount == 0:
                return jsonify({"code": 1, "message": "规划级别不存在"})

            connection.commit()
            return jsonify({"code": 0, "message": "更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新国家规划级别失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/national_regulation_levels/<int:level_id>', methods=['DELETE'])
def delete_national_regulation_level(level_id):
    """删除国家规划级别"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否有样书使用该规划级别
            cursor.execute("SELECT COUNT(*) as count FROM sample_books WHERE national_regulation_level_id = %s", (level_id,))
            result = cursor.fetchone()
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": f"该规划级别正在被 {result['count']} 本样书使用，无法删除"})

            # 删除记录
            cursor.execute("DELETE FROM national_regulation_levels WHERE id = %s", (level_id,))

            if cursor.rowcount == 0:
                return jsonify({"code": 1, "message": "规划级别不存在"})

            connection.commit()
            return jsonify({"code": 0, "message": "删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除国家规划级别失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

# 省级规划级别管理API
@admin_bp.route('/provincial_regulation_levels', methods=['GET'])
def get_provincial_regulation_levels_new():
    """获取省级规划级别列表"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM provincial_regulation_levels ORDER BY created_at DESC")
            levels = cursor.fetchall()

            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": levels
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取省级规划级别列表失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/provincial_regulation_levels', methods=['POST'])
def add_provincial_regulation_level():
    """添加省级规划级别"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "规划级别名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否已存在
            cursor.execute("SELECT id FROM provincial_regulation_levels WHERE name = %s", (name,))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该规划级别已存在"})

            # 插入新记录
            cursor.execute(
                "INSERT INTO provincial_regulation_levels (name, description) VALUES (%s, %s)",
                (name, description if description else None)
            )
            connection.commit()

            return jsonify({"code": 0, "message": "添加成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加省级规划级别失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/provincial_regulation_levels/<int:level_id>', methods=['PUT'])
def update_provincial_regulation_level(level_id):
    """更新省级规划级别"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({"code": 1, "message": "规划级别名称不能为空"})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否存在同名记录（排除当前记录）
            cursor.execute("SELECT id FROM provincial_regulation_levels WHERE name = %s AND id != %s", (name, level_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该规划级别名称已存在"})

            # 更新记录
            cursor.execute(
                "UPDATE provincial_regulation_levels SET name = %s, description = %s WHERE id = %s",
                (name, description if description else None, level_id)
            )

            if cursor.rowcount == 0:
                return jsonify({"code": 1, "message": "规划级别不存在"})

            connection.commit()
            return jsonify({"code": 0, "message": "更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新省级规划级别失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

@admin_bp.route('/provincial_regulation_levels/<int:level_id>', methods=['DELETE'])
def delete_provincial_regulation_level(level_id):
    """删除省级规划级别"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})

    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查是否有样书使用该规划级别
            cursor.execute("SELECT COUNT(*) as count FROM sample_books WHERE provincial_regulation_level_id = %s", (level_id,))
            result = cursor.fetchone()
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": f"该规划级别正在被 {result['count']} 本样书使用，无法删除"})

            # 删除记录
            cursor.execute("DELETE FROM provincial_regulation_levels WHERE id = %s", (level_id,))

            if cursor.rowcount == 0:
                return jsonify({"code": 1, "message": "规划级别不存在"})

            connection.commit()
            return jsonify({"code": 0, "message": "删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除省级规划级别失败: {str(e)}"})
    finally:
        if 'connection' in locals():
            connection.close()

# ==================== 换版推荐管理 ====================

@admin_bp.route('/get_book_recommendations', methods=['GET'])
def get_book_recommendations():
    """
    获取换版推荐列表（管理员）
    支持多维度筛选和分页
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 获取筛选参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        # 筛选条件
        status = request.args.get('status', '')  # 推荐状态
        recommendation_type = request.args.get('recommendation_type', '')  # 推荐类型
        school_id = request.args.get('school_id', '')  # 学校ID
        initiator_company_id = request.args.get('initiator_company_id', '')  # 发起公司ID
        supplier_id = request.args.get('supplier_id', '')  # 供应商ID
        keyword = request.args.get('keyword', '')  # 关键词搜索

        # 时间筛选
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 构建查询条件
                where_conditions = []
                params = []

                if status:
                    where_conditions.append("br.status = %s")
                    params.append(status)

                if recommendation_type:
                    where_conditions.append("br.recommendation_type = %s")
                    params.append(recommendation_type)

                if school_id:
                    where_conditions.append("br.school_id = %s")
                    params.append(school_id)

                if initiator_company_id:
                    where_conditions.append("br.initiator_company_id = %s")
                    params.append(initiator_company_id)

                if supplier_id:
                    where_conditions.append("br.original_book_supplier_id = %s")
                    params.append(supplier_id)

                if keyword:
                    where_conditions.append("(s.name LIKE %s OR sb.name LIKE %s OR sb.isbn LIKE %s)")
                    keyword_param = f"%{keyword}%"
                    params.extend([keyword_param, keyword_param, keyword_param])

                if start_date:
                    where_conditions.append("DATE(br.created_at) >= %s")
                    params.append(start_date)

                if end_date:
                    where_conditions.append("DATE(br.created_at) <= %s")
                    params.append(end_date)

                where_clause = " AND " + " AND ".join(where_conditions) if where_conditions else ""

                # 获取总数
                count_sql = f"""
                    SELECT COUNT(*) as total
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    WHERE 1=1 {where_clause}
                """
                cursor.execute(count_sql, params)
                total = cursor.fetchone()['total']

                # 获取推荐列表
                offset = (page - 1) * per_page
                list_sql = f"""
                    SELECT br.id, br.initiator_id, br.initiator_company_id, br.school_id,
                           br.original_book_id, br.original_book_supplier_id, br.replacement_reason,
                           br.replacement_reason_other, br.requirement_no_monopoly, br.requirement_recent_publish,
                           br.requirement_sufficient_stock, br.requirement_national_priority, br.requirement_other,
                           br.recommendation_type, br.status, br.referrer_id, br.created_at, br.updated_at,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           s.name as school_name, u.name as initiator_name,
                           sb.name as original_book_name, sb.isbn as original_book_isbn,
                           sb.publisher_name as original_book_publisher, sb.price as original_book_price,
                           sb.publication_date as original_book_publication_date,
                           pc.name as supplier_name, dc.name as initiator_company_name,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id) as result_count,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id AND status = 'approved') as approved_count
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN users u ON br.initiator_id = u.user_id
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    LEFT JOIN publisher_companies pc ON br.original_book_supplier_id = pc.id
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    WHERE 1=1 {where_clause}
                    ORDER BY br.created_at DESC
                    LIMIT %s OFFSET %s
                """
                cursor.execute(list_sql, params + [per_page, offset])
                recommendations = cursor.fetchall()

                # 格式化日期字段
                for rec in recommendations:
                    if rec.get('original_book_publication_date'):
                        rec['original_book_publication_date'] = rec['original_book_publication_date'].strftime('%Y-%m-%d')
                    if rec.get('created_at'):
                        rec['created_at'] = rec['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if rec.get('updated_at'):
                        rec['updated_at'] = rec['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

                return jsonify({
                    "code": 0,
                    "data": {
                        "recommendations": recommendations,
                        "total": total,
                        "page": page,
                        "per_page": per_page,
                        "total_pages": (total + per_page - 1) // per_page
                    }
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取推荐列表失败: {str(e)}"})

@admin_bp.route('/get_book_recommendation_detail', methods=['GET'])
def get_book_recommendation_detail():
    """
    获取换版推荐详情（管理员）
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    recommendation_id = request.args.get('id')
    if not recommendation_id:
        return jsonify({"code": 1, "message": "缺少推荐ID"})

    try:
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取推荐详情
                sql = """
                    SELECT br.id, br.initiator_id, br.initiator_company_id, br.school_id,
                           br.original_book_id, br.original_book_supplier_id, br.replacement_reason,
                           br.replacement_reason_other, br.requirement_no_monopoly, br.requirement_recent_publish,
                           br.requirement_sufficient_stock, br.requirement_national_priority, br.requirement_other,
                           br.recommendation_type, br.status, br.referrer_id, br.created_at, br.updated_at, br.notes,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           s.name as school_name,
                           u.name as initiator_name, u.email as initiator_email,
                           sb.name as original_book_name, sb.isbn as original_book_isbn,
                           sb.author as original_book_author, sb.publisher_name as original_book_publisher,
                           sb.price as original_book_price, sb.publication_date as original_book_publication_date,
                           pc.name as supplier_name, dc.name as initiator_company_name,
                           ref_u.name as referrer_name
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN users u ON br.initiator_id = u.user_id
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    LEFT JOIN publisher_companies pc ON br.original_book_supplier_id = pc.id
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    LEFT JOIN users ref_u ON br.referrer_id = ref_u.user_id
                    WHERE br.id = %s
                """
                cursor.execute(sql, (recommendation_id,))
                recommendation = cursor.fetchone()

                if not recommendation:
                    return jsonify({"code": 1, "message": "推荐不存在"})

                # 获取推荐结果（包含冲突检查）
                sql = """
                    SELECT rr.*, sb.name as book_name, sb.author, sb.isbn, sb.publisher_name,
                           sb.price, sb.publication_date, u.name as recommender_name,
                           CASE
                               WHEN u.role = 'publisher' THEN pc.name
                               WHEN u.role = 'dealer' THEN dc.name
                               ELSE u.name
                           END as recommender_company,
                           u.role as recommender_role,
                           -- 使用已存储的包销冲突标志
                           rr.is_monopoly_conflict,
                           -- 检查出版时间冲突（实时计算）
                           CASE
                               WHEN br.requirement_recent_publish = 1 AND sb.publication_date IS NOT NULL
                                    AND DATEDIFF(br.created_at, sb.publication_date) > 1095 THEN 1
                               ELSE 0
                           END as is_publication_conflict
                    FROM recommendation_results rr
                    JOIN sample_books sb ON rr.recommended_book_id = sb.id
                    JOIN users u ON rr.recommender_id = u.user_id
                    JOIN book_recommendations br ON rr.recommendation_id = br.id
                    LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                    LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                    WHERE rr.recommendation_id = %s
                    ORDER BY rr.created_at ASC
                """
                cursor.execute(sql, (recommendation_id,))
                results = cursor.fetchall()

                # 格式化日期字段
                if recommendation.get('original_book_publication_date'):
                    recommendation['original_book_publication_date'] = recommendation['original_book_publication_date'].strftime('%Y-%m-%d')
                if recommendation.get('created_at'):
                    recommendation['created_at'] = recommendation['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if recommendation.get('updated_at'):
                    recommendation['updated_at'] = recommendation['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

                for result in results:
                    if result.get('publication_date'):
                        result['publication_date'] = result['publication_date'].strftime('%Y-%m-%d')
                    if result.get('created_at'):
                        result['created_at'] = result['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                    if result.get('updated_at'):
                        result['updated_at'] = result['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

                return jsonify({
                    "code": 0,
                    "data": {
                        "recommendation": recommendation,
                        "results": results
                    }
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取推荐详情失败: {str(e)}"})

@admin_bp.route('/update_recommendation_status', methods=['POST'])
def update_recommendation_status():
    """
    更新换版推荐状态（管理员）
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.json
        recommendation_id = data.get('recommendation_id')
        new_status = data.get('status')
        notes = data.get('notes', '')

        if not recommendation_id or not new_status:
            return jsonify({"code": 1, "message": "缺少必要参数"})

        if new_status not in ['in_progress', 'ended']:
            return jsonify({"code": 1, "message": "无效的状态值"})

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查推荐是否存在
                cursor.execute("SELECT id, status FROM book_recommendations WHERE id = %s", (recommendation_id,))
                recommendation = cursor.fetchone()

                if not recommendation:
                    return jsonify({"code": 1, "message": "推荐不存在"})

                # 更新状态
                cursor.execute("""
                    UPDATE book_recommendations
                    SET status = %s, notes = %s, updated_at = NOW()
                    WHERE id = %s
                """, (new_status, notes, recommendation_id))

                connection.commit()
                return jsonify({"code": 0, "message": "状态更新成功"})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新状态失败: {str(e)}"})

@admin_bp.route('/delete_recommendation_result', methods=['POST'])
def delete_recommendation_result():
    """
    删除推荐结果（管理员）
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        data = request.json
        result_id = data.get('result_id')
        reason = data.get('reason', '')

        if not result_id:
            return jsonify({"code": 1, "message": "缺少推荐结果ID"})

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取推荐结果详情
                cursor.execute("""
                    SELECT rr.*, br.school_id, s.name as school_name, br.initiator_id
                    FROM recommendation_results rr
                    JOIN book_recommendations br ON rr.recommendation_id = br.id
                    JOIN schools s ON br.school_id = s.id
                    WHERE rr.id = %s
                """, (result_id,))
                result = cursor.fetchone()

                if not result:
                    return jsonify({"code": 1, "message": "推荐结果不存在"})

                # 删除对应的报备记录（如果是通过换版推荐自动生成的）
                cursor.execute("""
                    DELETE FROM promotion_reports
                    WHERE sample_book_id = %s
                    AND school_name = %s
                    AND dealer_id = %s
                    AND reason = '通过换版推荐自动生成'
                """, (result['recommended_book_id'], result['school_name'], result['initiator_id']))

                # 删除推荐结果
                cursor.execute("DELETE FROM recommendation_results WHERE id = %s", (result_id,))

                connection.commit()
                return jsonify({"code": 0, "message": "推荐结果删除成功"})
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除推荐结果失败: {str(e)}"})

@admin_bp.route('/get_recommendation_statistics', methods=['GET'])
def get_recommendation_statistics():
    """
    获取换版推荐统计数据（管理员）
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 基础统计
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_recommendations,
                        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_count,
                        SUM(CASE WHEN status = 'ended' THEN 1 ELSE 0 END) as ended_count,
                        SUM(CASE WHEN recommendation_type = 'direct' THEN 1 ELSE 0 END) as direct_count,
                        SUM(CASE WHEN recommendation_type = 'internal' THEN 1 ELSE 0 END) as internal_count,
                        SUM(CASE WHEN recommendation_type = 'external' THEN 1 ELSE 0 END) as external_count
                    FROM book_recommendations
                """)
                basic_stats = cursor.fetchone()

                # 推荐结果统计
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_results,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
                        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count
                    FROM recommendation_results
                """)
                result_stats = cursor.fetchone()

                # 最近7天的推荐趋势
                cursor.execute("""
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as count
                    FROM book_recommendations
                    WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """)
                trend_data = cursor.fetchall()

                # 热门原用教材（被推荐次数最多的）
                cursor.execute("""
                    SELECT
                        sb.name as book_name,
                        sb.isbn,
                        sb.publisher_name,
                        COUNT(*) as recommendation_count
                    FROM book_recommendations br
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    GROUP BY br.original_book_id
                    ORDER BY recommendation_count DESC
                    LIMIT 10
                """)
                popular_books = cursor.fetchall()

                # 活跃经销商公司
                cursor.execute("""
                    SELECT
                        dc.name as company_name,
                        COUNT(*) as recommendation_count
                    FROM book_recommendations br
                    JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    GROUP BY br.initiator_company_id
                    ORDER BY recommendation_count DESC
                    LIMIT 10
                """)
                active_dealers = cursor.fetchall()

                return jsonify({
                    "code": 0,
                    "data": {
                        "basic_stats": basic_stats,
                        "result_stats": result_stats,
                        "trend_data": trend_data,
                        "popular_books": popular_books,
                        "active_dealers": active_dealers
                    }
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取统计数据失败: {str(e)}"})

@admin_bp.route('/get_recommendation_filter_options', methods=['GET'])
def get_recommendation_filter_options():
    """
    获取换版推荐筛选选项（管理员）
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取学校列表
                cursor.execute("""
                    SELECT DISTINCT s.id, s.name, s.school_level
                    FROM schools s
                    JOIN book_recommendations br ON s.id = br.school_id
                    ORDER BY s.name
                """)
                schools = cursor.fetchall()

                # 获取经销商公司列表
                cursor.execute("""
                    SELECT DISTINCT dc.id, dc.name
                    FROM dealer_companies dc
                    JOIN book_recommendations br ON dc.id = br.initiator_company_id
                    ORDER BY dc.name
                """)
                dealer_companies = cursor.fetchall()

                # 获取出版社公司列表
                cursor.execute("""
                    SELECT DISTINCT pc.id, pc.name
                    FROM publisher_companies pc
                    JOIN book_recommendations br ON pc.id = br.original_book_supplier_id
                    ORDER BY pc.name
                """)
                publisher_companies = cursor.fetchall()

                # 状态选项
                status_options = [
                    {"value": "in_progress", "text": "推荐中"},
                    {"value": "ended", "text": "已结束"}
                ]

                # 推荐类型选项
                type_options = [
                    {"value": "direct", "text": "直接推荐"},
                    {"value": "internal", "text": "内部推荐"},
                    {"value": "external", "text": "外部推荐"}
                ]

                return jsonify({
                    "code": 0,
                    "data": {
                        "schools": schools,
                        "dealer_companies": dealer_companies,
                        "publisher_companies": publisher_companies,
                        "status_options": status_options,
                        "type_options": type_options
                    }
                })
        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取筛选选项失败: {str(e)}"})

@admin_bp.route('/export_recommendations', methods=['GET'])
def export_recommendations():
    """
    导出换版推荐数据（管理员）
    """
    # 检查登录状态和管理员权限
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        # 获取筛选参数（与列表接口相同）
        status = request.args.get('status', '')
        recommendation_type = request.args.get('recommendation_type', '')
        school_id = request.args.get('school_id', '')
        initiator_company_id = request.args.get('initiator_company_id', '')
        supplier_id = request.args.get('supplier_id', '')
        keyword = request.args.get('keyword', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 构建查询条件
                where_conditions = []
                params = []

                if status:
                    where_conditions.append("br.status = %s")
                    params.append(status)

                if recommendation_type:
                    where_conditions.append("br.recommendation_type = %s")
                    params.append(recommendation_type)

                if school_id:
                    where_conditions.append("br.school_id = %s")
                    params.append(school_id)

                if initiator_company_id:
                    where_conditions.append("br.initiator_company_id = %s")
                    params.append(initiator_company_id)

                if supplier_id:
                    where_conditions.append("br.original_book_supplier_id = %s")
                    params.append(supplier_id)

                if keyword:
                    where_conditions.append("(s.name LIKE %s OR sb.name LIKE %s OR sb.isbn LIKE %s)")
                    keyword_param = f"%{keyword}%"
                    params.extend([keyword_param, keyword_param, keyword_param])

                if start_date:
                    where_conditions.append("DATE(br.created_at) >= %s")
                    params.append(start_date)

                if end_date:
                    where_conditions.append("DATE(br.created_at) <= %s")
                    params.append(end_date)

                where_clause = " AND " + " AND ".join(where_conditions) if where_conditions else ""

                # 获取推荐数据
                sql = f"""
                    SELECT br.id, s.name as school_name,
                           COALESCE(NULLIF(br.school_level, ''), s.school_level) as school_level,
                           sb.name as original_book_name, sb.isbn as original_book_isbn,
                           sb.author as original_book_author, sb.publisher_name as original_book_publisher,
                           sb.price as original_book_price, sb.publication_date as original_book_publication_date,
                           pc.name as supplier_name, dc.name as initiator_company_name, u.name as initiator_name,
                           br.recommendation_type, br.status, br.replacement_reason, br.replacement_reason_other,
                           br.requirement_no_monopoly, br.requirement_recent_publish, br.requirement_sufficient_stock,
                           br.requirement_national_priority, br.requirement_other,
                           (SELECT COUNT(*) FROM recommendation_results WHERE recommendation_id = br.id) as result_count,
                           br.created_at, br.updated_at
                    FROM book_recommendations br
                    JOIN schools s ON br.school_id = s.id
                    JOIN users u ON br.initiator_id = u.user_id
                    JOIN sample_books sb ON br.original_book_id = sb.id
                    LEFT JOIN publisher_companies pc ON br.original_book_supplier_id = pc.id
                    LEFT JOIN dealer_companies dc ON br.initiator_company_id = dc.id
                    WHERE 1=1 {where_clause}
                    ORDER BY br.created_at DESC
                """
                cursor.execute(sql, params)
                recommendations = cursor.fetchall()

                # 获取每个推荐的推荐书目
                recommendation_results = {}
                for rec in recommendations:
                    cursor.execute("""
                        SELECT rr.*, sb.name as book_name, sb.author, sb.isbn, sb.publisher_name,
                               sb.price, sb.publication_date, u.name as recommender_name,
                               CASE
                                   WHEN u.role = 'publisher' THEN pc.name
                                   WHEN u.role = 'dealer' THEN dc.name
                                   ELSE u.name
                               END as recommender_company,
                               u.role as recommender_role, rr.is_monopoly_conflict,
                               CASE
                                   WHEN br.requirement_recent_publish = 1 AND sb.publication_date IS NOT NULL
                                        AND DATEDIFF(br.created_at, sb.publication_date) > 1095 THEN 1
                                   ELSE 0
                               END as is_publication_conflict
                        FROM recommendation_results rr
                        JOIN sample_books sb ON rr.recommended_book_id = sb.id
                        JOIN users u ON rr.recommender_id = u.user_id
                        JOIN book_recommendations br ON rr.recommendation_id = br.id
                        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                        WHERE rr.recommendation_id = %s
                        ORDER BY rr.created_at ASC
                    """, (rec['id'],))
                    recommendation_results[rec['id']] = cursor.fetchall()

                # 创建Excel工作簿
                from openpyxl import Workbook
                from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

                wb = Workbook()
                ws = wb.active
                ws.title = "换版推荐数据"

                # 设置样式
                header_font = Font(bold=True, size=12)
                subheader_font = Font(bold=True, size=10)
                normal_font = Font(size=10)
                header_fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")
                subheader_fill = PatternFill(start_color="F0F8FF", end_color="F0F8FF", fill_type="solid")
                border = Border(left=Side(style='thin'), right=Side(style='thin'),
                               top=Side(style='thin'), bottom=Side(style='thin'))

                current_row = 1

                for rec in recommendations:
                    # 推荐基本信息标题
                    ws.merge_cells(f'A{current_row}:H{current_row}')
                    cell = ws[f'A{current_row}']
                    cell.value = f"换版推荐 - {rec['school_name']} - {rec['original_book_name']}"
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal='center')
                    cell.border = border
                    current_row += 1

                    # 基本信息
                    info_data = [
                        ['学校名称', rec['school_name'], '学校层次', rec['school_level'] or '未分类'],
                        ['发起公司', rec['initiator_company_name'], '发起人', rec['initiator_name']],
                        ['推荐类型', {'direct': '直接推荐', 'internal': '内部推荐', 'external': '外部推荐'}.get(rec['recommendation_type'], rec['recommendation_type']),
                         '状态', {'in_progress': '推荐中', 'ended': '已结束'}.get(rec['status'], rec['status'])],
                        ['创建时间', rec['created_at'].strftime('%Y-%m-%d %H:%M:%S'), '推荐总数', rec['result_count']]
                    ]

                    for row_data in info_data:
                        for col, value in enumerate(row_data, 1):
                            cell = ws.cell(row=current_row, column=col, value=value)
                            cell.font = normal_font
                            cell.border = border
                            if col % 2 == 1:  # 标签列
                                cell.font = subheader_font
                                cell.fill = subheader_fill
                        current_row += 1

                    # 原用教材信息
                    ws.merge_cells(f'A{current_row}:H{current_row}')
                    cell = ws[f'A{current_row}']
                    cell.value = "原用教材信息"
                    cell.font = subheader_font
                    cell.fill = subheader_fill
                    cell.border = border
                    current_row += 1

                    original_book_data = [
                        ['教材名称', rec['original_book_name'], 'ISBN', rec['original_book_isbn']],
                        ['作者', rec['original_book_author'], '出版社', rec['original_book_publisher']],
                        ['价格', f"¥{rec['original_book_price']}" if rec['original_book_price'] else '未知',
                         '出版日期', rec['original_book_publication_date'].strftime('%Y-%m-%d') if rec['original_book_publication_date'] else '未知']
                    ]

                    for row_data in original_book_data:
                        for col, value in enumerate(row_data, 1):
                            cell = ws.cell(row=current_row, column=col, value=value)
                            cell.font = normal_font
                            cell.border = border
                            if col % 2 == 1:  # 标签列
                                cell.font = subheader_font
                                cell.fill = subheader_fill
                        current_row += 1

                    # 换版原因
                    ws.merge_cells(f'A{current_row}:H{current_row}')
                    cell = ws[f'A{current_row}']
                    cell.value = "换版原因"
                    cell.font = subheader_font
                    cell.fill = subheader_fill
                    cell.border = border
                    current_row += 1

                    reason_text = {'no_reprint': '不再重印', 'new_book_not_published': '新书未出版',
                                  'no_supplier': '无供应商', 'other': '其他'}.get(rec['replacement_reason'], rec['replacement_reason'])

                    reason_data = [['换版原因', reason_text, '', '']]
                    if rec['replacement_reason_other']:
                        reason_data.append(['其他原因', rec['replacement_reason_other'], '', ''])

                    for row_data in reason_data:
                        for col, value in enumerate(row_data, 1):
                            cell = ws.cell(row=current_row, column=col, value=value)
                            cell.font = normal_font
                            cell.border = border
                            if col == 1:  # 标签列
                                cell.font = subheader_font
                                cell.fill = subheader_fill
                        current_row += 1

                    # 推荐要求
                    ws.merge_cells(f'A{current_row}:H{current_row}')
                    cell = ws[f'A{current_row}']
                    cell.value = "推荐要求"
                    cell.font = subheader_font
                    cell.fill = subheader_fill
                    cell.border = border
                    current_row += 1

                    requirements = []
                    if rec['requirement_no_monopoly']: requirements.append('禁用包销书')
                    if rec['requirement_recent_publish']: requirements.append('近三年出版')
                    if rec['requirement_sufficient_stock']: requirements.append('库存充足')
                    if rec['requirement_national_priority']: requirements.append('国规优先')

                    if requirements:
                        req_text = '、'.join(requirements)
                    else:
                        req_text = '无特殊要求'

                    req_data = [['基础要求', req_text, '', '']]
                    if rec['requirement_other']:
                        req_data.append(['其他要求', rec['requirement_other'], '', ''])

                    for row_data in req_data:
                        for col, value in enumerate(row_data, 1):
                            cell = ws.cell(row=current_row, column=col, value=value)
                            cell.font = normal_font
                            cell.border = border
                            if col == 1:  # 标签列
                                cell.font = subheader_font
                                cell.fill = subheader_fill
                        current_row += 1

                    # 推荐书目
                    results = recommendation_results.get(rec['id'], [])
                    if results:
                        ws.merge_cells(f'A{current_row}:H{current_row}')
                        cell = ws[f'A{current_row}']
                        cell.value = f"推荐书目 ({len(results)}本)"
                        cell.font = subheader_font
                        cell.fill = subheader_fill
                        cell.border = border
                        current_row += 1

                        # 推荐书目表头
                        book_headers = ['序号', '书名', 'ISBN', '作者', '出版社', '价格', '推荐人', '冲突提示']
                        for col, header in enumerate(book_headers, 1):
                            cell = ws.cell(row=current_row, column=col, value=header)
                            cell.font = subheader_font
                            cell.fill = subheader_fill
                            cell.border = border
                        current_row += 1

                        # 推荐书目数据
                        for idx, result in enumerate(results, 1):
                            conflicts = []
                            if result['is_monopoly_conflict']:
                                conflicts.append('包销冲突')
                            if result['is_publication_conflict']:
                                conflicts.append('出版时间冲突')
                            conflict_text = '、'.join(conflicts) if conflicts else '无冲突'

                            book_data = [
                                idx, result['book_name'], result['isbn'], result['author'],
                                result['publisher_name'], f"¥{result['price']}" if result['price'] else '未知',
                                f"{result['recommender_name']}({result['recommender_company']})", conflict_text
                            ]

                            for col, value in enumerate(book_data, 1):
                                cell = ws.cell(row=current_row, column=col, value=value)
                                cell.font = normal_font
                                cell.border = border
                                # 如果有冲突，标红
                                if col == 8 and conflicts:
                                    cell.font = Font(color="FF0000", size=10)
                            current_row += 1

                    # 添加空行分隔
                    current_row += 2

                # 调整列宽
                column_widths = [8, 25, 15, 15, 20, 12, 25, 15]
                for i, width in enumerate(column_widths, 1):
                    ws.column_dimensions[chr(64 + i)].width = width

                # 创建临时文件
                with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
                    wb.save(tmp_file.name)
                    filename = f"换版推荐详细数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                    return send_file(tmp_file.name, as_attachment=True, download_name=filename)

        finally:
            connection.close()
    except Exception as e:
        return jsonify({"code": 1, "message": f"导出数据失败: {str(e)}"})